# Create Omnia Resources for orderhub. managed Identity, KeyVault, availability test, Redis Cache
[CmdletBinding()]
param (
    $SUBSCRIPTION_ID,
    $ENV,
    $KEYVAULT_NAME
)

$SERVICEBUSNAME = "sb-omnia-$ENV"
$RESOURCE_NAME  = "-ingestion-$ENV"

$RESOURCEGROUP  = "rg-omnia-$env"
$MANAGED_ID_NAME = "umi$RESOURCE_NAME"

az account set --subscription $SUBSCRIPTION_ID

$CLIENT_ID = az ad sp list --display-name $MANAGED_ID_NAME --query [].id --output tsv

# Omnia service bus
az role assignment create --role "Azure Service Bus Data Owner" --assignee $CLIENT_ID --scope /subscriptions/$SUBSCRIPTION_ID/resourceGroups/$RESOURCEGROUP/providers/Microsoft.ServiceBus/namespaces/$SERVICEBUSNAME

# assign rights to managed identity
$AGENTPOOL_CLIENT_ID = az ad sp list --display-name "aks-omnia-$ENV-agentpool" --query [].id --output tsv
az role assignment create --assignee $AGENTPOOL_CLIENT_ID --role "Managed Identity Operator" --scope "/subscriptions/$SUBSCRIPTION_ID/resourcegroups/rg-omnia-$ENV-identities/providers/Microsoft.ManagedIdentity/userAssignedIdentities/$MANAGED_ID_NAME"

az keyvault set-policy --name $KEYVAULT_NAME --resource-group $RESOURCEGROUP --object-id $CLIENT_ID --secret-permissions get, list, set
az role assignment create --role "Key Vault Secrets User" --assignee $CLIENT_ID --scope /subscriptions/$SUBSCRIPTION_ID/resourceGroups/$RESOURCEGROUP/providers/Microsoft.KeyVault/vaults/$KEYVAULT_NAME