﻿using Amazon;
using Amazon.Runtime;
using Amazon.SQS;
using Amazon.SQS.Model;
using FluentResults;
using FluentValidation;
using Newtonsoft.Json.Linq;
using Pondres.Omnia.Ingestion.Common.Model;
using Pondres.Omnia.Ingestion.Common.Provider;
using Pondres.Omnia.Ingestion.Common.Service;
using Serilog;
using System.Text;
using System.Text.RegularExpressions;

namespace Pondres.Omnia.Ingestion.Amazon.Commands;

public record ReadAwsQueueCommand(
    string QueueUrl,
    string FlowName,
    string Customer,
    string CustomerStorageAccountName,
    string CustomerVaultName,
    string AccessKeySecretName,
    string SecretKeySecretName,
    CustomerReferenceDeterminationType CustomerReferenceDeterminationType,
    string CustomerReferenceDeterminationTypeValue);

public class ReadAwsQueueCommandValidator : AbstractValidator<ReadAwsQueueCommand>
{
    public ReadAwsQueueCommandValidator()
    {
        RuleFor(x => x.QueueUrl).NotEmpty();
        RuleFor(x => x.FlowName).NotEmpty();
        RuleFor(x => x.AccessKeySecretName).NotEmpty();
        RuleFor(x => x.SecretKeySecretName).NotEmpty();
        RuleFor(x => x.CustomerReferenceDeterminationTypeValue).NotEmpty().When(x => x.CustomerReferenceDeterminationType == CustomerReferenceDeterminationType.JsonPath);
        RuleFor(x => x.CustomerReferenceDeterminationType).NotEmpty().NotEqual(CustomerReferenceDeterminationType.None);
    }
}

public class ReadAwsQueueCommandHandler(ISecretProvider secretProvider, IOrderService orderService, IEventService eventService)
{
    // Maximum number of iterations to try to get messages from the queue, 10 messages at a time
    private readonly int maxIterations = 100;

    public async Task<Result> HandleAsync(ReadAwsQueueCommand request)
    {
        try
        {
            var validationResult = new ReadAwsQueueCommandValidator().Validate(request);

            if (!validationResult.IsValid)
                return Result.Fail(validationResult.Errors.Select(x => x.ErrorMessage));

            string? accessKey;
            string? secretKey;

            try
            {
                accessKey = await secretProvider.GetSecretAsync(request.CustomerVaultName, request.AccessKeySecretName);
                secretKey = await secretProvider.GetSecretAsync(request.CustomerVaultName, request.SecretKeySecretName);

                if (string.IsNullOrEmpty(accessKey) || string.IsNullOrEmpty(secretKey))
                    return Result.Fail("AccessKey or SecretKey is empty");
            }
            catch (Exception exception)
            {
                Log.Error(exception, "Exception during secret retrieval");
                return new ExceptionalError(exception);
            }

            var region = GetRegionFromQueueUrl(request.QueueUrl);

            var client = new AmazonSQSClient(new BasicAWSCredentials(accessKey, secretKey), region);

            // You can only get 10 messages at a time, we will try this 10 times
            for (var i = 0; i < maxIterations; i++)
            {
                var receiveResponse = await client.ReceiveMessageAsync(new ReceiveMessageRequest
                {
                    QueueUrl = request.QueueUrl,
                    MaxNumberOfMessages = 10, // 10 is the maximum
                    WaitTimeSeconds = 5
                });

                if (receiveResponse.Messages == null || receiveResponse.Messages.Count == 0)
                {
                    Log.Information("No new messages found in queue {QueueUrl}", request.QueueUrl);
                    break;
                }

                foreach (var message in receiveResponse.Messages)
                {
                    // Load body into stream
                    using var stream = new MemoryStream(Encoding.UTF8.GetBytes(message.Body));

                    var customerReference = ExtractCustomerReference(message, request.CustomerReferenceDeterminationType, request.CustomerReferenceDeterminationTypeValue);

                    if (string.IsNullOrEmpty(customerReference))
                    {
                        Log.Error("Customer reference could not be determined for message Id: {MessageId}", message.MessageId);

                        await eventService.PublishIngestionFailureAsync(
                            message: "Customer reference could not be determined",
                            data: $"Determination Value: {request.CustomerReferenceDeterminationTypeValue} Message body: {message.Body}",
                            requestId: Guid.NewGuid().ToString(),
                            flowName: request.FlowName,
                            customer: request.Customer);

                        continue;
                    }

                    await orderService.CreateSingleRawDataFileOrderAsync(stream, new OrderCreationModel(
                        CustomerReference: customerReference,
                        Categories: new OrderCreationModelCategories(),
                        Customer: request.Customer,
                        FileName: $"{message.MessageId}.json",
                        FlowName: request.FlowName,
                        FlowId: null,
                        OrderId: Guid.NewGuid(),
                        RequestId: message.MessageId,
                        SourceName: "AmazonSQS",
                        CustomerStorageAccountName: request.CustomerStorageAccountName
                    ));

                    // Delete message
                    await client.DeleteMessageAsync(new DeleteMessageRequest
                    {
                        QueueUrl = request.QueueUrl,
                        ReceiptHandle = message.ReceiptHandle
                    });
                }
            }

            return Result.Ok();

        }
        catch (Exception exception)
        {
            Log.Error(exception, "Exception during handling of ReadAwsQueueCommand");

            await eventService.PublishIngestionFailureAsync(exception, Guid.NewGuid().ToString(), request.FlowName, request.Customer);

            return new ExceptionalError(exception);
        }
    }

    private static RegionEndpoint GetRegionFromQueueUrl(string queueUrl)
    {
        var pattern = @"sqs\.(?<region>[a-z0-9-]+)\.amazonaws\.com";
        var match = Regex.Match(queueUrl, pattern);

        return match.Success
            ? RegionEndpoint.GetBySystemName(match.Groups["region"].Value)
            : throw new InvalidOperationException($"Could not determine region from queue url {queueUrl}");
    }

    private static string? ExtractCustomerReference(Message message, CustomerReferenceDeterminationType determinationType, string determinationValue)
    {
        if (determinationType == CustomerReferenceDeterminationType.JsonPath)
        {
            var json = JObject.Parse(message.Body);

            return json?.SelectToken(determinationValue)?.Value<string>();
        }
        else
            throw new NotImplementedException($"No customer reference determination type implemented for type {determinationType}");
    }
}
