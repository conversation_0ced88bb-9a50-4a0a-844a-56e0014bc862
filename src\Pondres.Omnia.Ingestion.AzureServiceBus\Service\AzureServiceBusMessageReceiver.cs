﻿using Microsoft.Azure.ServiceBus;
using Microsoft.Azure.ServiceBus.Core;
using Polly;
using Polly.Retry;

namespace Pondres.Omnia.Ingestion.AzureServiceBus.Service
{
    public class AzureServiceBusMessageReceiver(IMessageReceiver messageReceiver)
    {
        private readonly TimeSpan queueReadTimeout = TimeSpan.FromSeconds(2.5);

        private readonly AsyncRetryPolicy retryPolicy = Policy
            .Handle<ServiceBusException>(exception => exception.IsTransient)
            .WaitAndRetryForeverAsync((retryCount) => TimeSpan.FromSeconds(retryCount));

        public async Task AbandonAsync(IEnumerable<Message> messages)
        {
            foreach (var message in messages)
                await retryPolicy.ExecuteAsync(async () => await messageReceiver.AbandonAsync(message.SystemProperties.LockToken));
        }

        public async Task AbandonAsync(Message message)
        {
            await retryPolicy.ExecuteAsync(async () => await messageReceiver.AbandonAsync(message.SystemProperties.LockToken));
        }

        public async Task CompleteAsync(IEnumerable<Message> messages)
        {
            if (messages.Any())
                await retryPolicy.ExecuteAsync(async () => await messageReceiver.CompleteAsync(messages.Select(x => x.SystemProperties.LockToken)));
        }

        public async Task CompleteAsync(Message message)
        {
            await retryPolicy.ExecuteAsync(async () => await messageReceiver.CompleteAsync(message.SystemProperties.LockToken));
        }

        public async Task<IList<Message>> ReceiveAsync(int maxDocumentCount) =>
            await retryPolicy.ExecuteAsync(async () => await messageReceiver.ReceiveAsync(maxDocumentCount, operationTimeout: queueReadTimeout));
    }
}