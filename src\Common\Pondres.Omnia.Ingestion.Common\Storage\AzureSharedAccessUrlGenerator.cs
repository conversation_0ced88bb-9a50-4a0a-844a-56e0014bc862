﻿using Azure;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Azure.Storage.Sas;
using System;
using System.Threading.Tasks;

namespace Pondres.Omnia.Ingestion.Common.Storage;

public static class AzureSharedAccessUrlGenerator
{
    public static async Task<Uri> GetSharedAccessSignatureUrlForBlobAsync(BlobServiceClient client, BlobClient blobClient, TimeSpan expireTime)
    {
        var expiresOn = DateTimeOffset.UtcNow + expireTime;

        var delegationKey = await client.GetUserDelegationKeyAsync(DateTimeOffset.UtcNow, expiresOn);

        var urlInformation = GetSharedAccessSignatureForBlob(blobClient, delegationKey, expiresOn);

        return urlInformation;
    }

    private static Uri GetSharedAccessSignatureForBlob(
        BlobClient blobClient,
        Response<UserDelegationKey> delegationKey,
        DateTimeOffset expiresOn)
    {
        var builder = new BlobSasBuilder
        {
            BlobName = blobClient.Name,
            BlobContainerName = blobClient.BlobContainerName,
            Resource = "b",
            StartsOn = DateTimeOffset.UtcNow,
            ExpiresOn = expiresOn
        };

        builder.SetPermissions(BlobSasPermissions.Read);
        var queryParameters = builder.ToSasQueryParameters(delegationKey, blobClient.AccountName);

        var sasUri = new UriBuilder(blobClient.Uri)
        {
            Query = queryParameters.ToString()
        };

        return sasUri.Uri;
    }
}