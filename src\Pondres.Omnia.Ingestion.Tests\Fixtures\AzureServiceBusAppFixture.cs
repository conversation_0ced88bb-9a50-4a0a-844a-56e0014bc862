﻿using MassTransit;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Hosting;
using Pondres.Omnia.Ingestion.AzureServiceBus;
using Pondres.Omnia.Ingestion.AzureServiceBus.Services;
using Pondres.Omnia.Ingestion.Common.Provider;
using Solude.ApiBase.Contracts;

namespace Pondres.Omnia.Ingestion.Tests.Fixtures;

public class AzureServiceBusAppFixture(
    ContainersFixture containers,
    string cosmosDbEndpoint) : BaseAppFixture<Program>(containers)
{
    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureAppConfiguration((context, builder) =>
        {
            var configuration = new Dictionary<string, string?>
            {
                // Overwrite configuration with connectionstrings to the created services
                { "DataSeedDisabled", "true" },
                { "AzuriteConnectionString", Containers.Azurite.GetConnectionString() },
                { "RabbitConnectionString", Containers.RabbitMq.GetConnectionString() },     
                { "CosmosDbEndpoint", cosmosDbEndpoint },
                { "IsDevelopment", "true" }
            };

            builder.AddInMemoryCollection(configuration);
        });
        builder.ConfigureTestServices(services =>
        {
            services.Remove(services.Single(x => x.ServiceType == typeof(ISecretProvider)));
            services.AddSingleton<ISecretProvider>(new EnvironmentSecretProvider());

            services.RemoveHostedService<QueueReaderBackgroundService>();
            services.AddSingleton<QueueReaderBackgroundService>();

            services.RemoveAll<IDataSeed>();
        });

        builder.UseEnvironment("Development");
    }
}
