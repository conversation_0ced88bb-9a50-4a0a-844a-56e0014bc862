﻿using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Cosmos.Linq;
using Pondres.Omnia.Ingestion.Common.Config;
using Pondres.Omnia.Ingestion.Shopify.Entities;

namespace Pondres.Omnia.Ingestion.Shopify.Queries;
public class ShopifyEnvironmentQueries(CosmosClient client)
{
    public async Task<List<ShopifyEnvironmentEntity>> GetActiveEnvironmentsAsync()
    {
        var configurationContainer = client
            .GetDatabase(CosmosConfiguration.IngestionDatabaseName)
            .GetContainer(CosmosConfiguration.ConfigurationContainerName);

        var iterator = configurationContainer.GetItemLinqQueryable<ShopifyEnvironmentEntity>()
            .Where(x => x.Type == CosmosConfiguration.ConfigurationShopifyEnvironmentTypeName && x.Active)
            .ToFeedIterator();

        var entities = new List<ShopifyEnvironmentEntity>();

        while (iterator.HasMoreResults)
        {
            var response = await iterator.ReadNextAsync();
            entities.AddRange(response);
        }

        return entities;
    }
}
