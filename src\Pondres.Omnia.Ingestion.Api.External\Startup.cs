﻿using Microsoft.AspNetCore.Mvc.Authorization;
using Microsoft.AspNetCore.Mvc;
using Pondres.Omnia.Ingestion.Common.Extensions;
using Solude.ApiBase.Extensions;
using System.Text.Json.Serialization;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Options;
using System.Security.Claims;
using System.Text.Encodings.Web;
using Pondres.Omnia.Ingestion.Api.External.Authentication;
using Pondres.Omnia.Ingestion.Common.Config;
using Pondres.Omnia.Ingestion.Common.Handlers;
using Pondres.Omnia.Ingestion.Common.Provider;

namespace Pondres.Omnia.Ingestion.Api.External;

public class Startup(IHostEnvironment environment, IConfiguration configuration)
{
    private const string schemeName = "CustomerToken";

    private readonly IngestionAppSettings appSettings = configuration.Get<IngestionAppSettings>() ??
        throw new InvalidOperationException("AppSettings not bindable");

    public void Configure(IApplicationBuilder app)
    {
        app.UseDefaultApiDependencies(Program.Name, appSettings);
    }

    public void ConfigureServices(IServiceCollection services)
    {
        services.AddCommonDependencies(environment, appSettings);

        services.AddTransient<CreateOrderCommandHandler>();
        services.AddTransient<CustomerAuthenticator>();
        services.AddTransient<KeyVaultSecretProvider>();

        services.AddCommonDependencies(appSettings, Program.Name)
            .AddSwaggerIfEnabled(appSettings.SwaggerEnabled)
            .AddControllers(delegate (MvcOptions options)
            {
                options.Filters.Add(new AuthorizeFilter());
            })
            .AddJsonOptions(delegate (JsonOptions option)
            {
                option.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
            });

        services.AddAuthentication(delegate (AuthenticationOptions options)
        {
            options.DefaultAuthenticateScheme = schemeName;
            options.DefaultChallengeScheme = schemeName;
        }).AddScheme<AuthenticationSchemeOptions, CustomerAuthenticationHandler>(schemeName, schemeName, null);

        services.AddAuthorization(delegate (AuthorizationOptions options)
        {
            var defaultPolicy = new AuthorizationPolicyBuilder().RequireAuthenticatedUser().Build();
            options.DefaultPolicy = defaultPolicy;
        });
    }
}

public class CustomerAuthenticationHandler(
    IOptionsMonitor<AuthenticationSchemeOptions> options,
    ILoggerFactory logger,
    UrlEncoder encoder,
    CustomerAuthenticator customerAuthenticator)
    : AuthenticationHandler<AuthenticationSchemeOptions>(options, logger, encoder)
{
    protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        Request.Headers.TryGetValue("x-token", out var tokenFromHeader);
        Request.Headers.TryGetValue("x-customer", out var customerFromHeader);

        return await customerAuthenticator.AuthenticateAsync(customerFromHeader, tokenFromHeader)
            ? AuthenticateResult.Success(
                new AuthenticationTicket(new ClaimsPrincipal(new ClaimsIdentity(
                        [
                            new Claim("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name",
                                customerFromHeader.ToString())
                        ],
                        base.Scheme.Name)),
                    base.Scheme.Name))
            : AuthenticateResult.NoResult();
    }
}