version: '3.4'

services:
  pondres.omnia.ingestion.amazon:
    image: ${DOCKER_REGISTRY-}pondresomniaingestionamazon
    build:
      context: ..
      dockerfile: src/Pondres.Omnia.Ingestion.Amazon/Dockerfile
    environment:
      - RabbitConnectionString=amqp://guest:guest@rabbitmq:5672
      - MessageBus=RabbitMq
      - CosmosDbEndpoint=${DevelopCosmosDbEndpoint}
      - SECRET_kv_k00021_test_AwsSqsAccessKey=${AwsSqsAccessKey}
      - SECRET_kv_k00021_test_AwsSqsSecretKey=${AwsSqsAccessSecret}
 
  pondres.omnia.ingestion.azureservicebus:
    image: ${DOCKER_REGISTRY-}pondresomniaingestionazureservicebus
    build:
      context: ..
      dockerfile: src/Pondres.Omnia.Ingestion.AzureServiceBus/Dockerfile
    environment:
      - RabbitConnectionString=amqp://guest:guest@rabbitmq:5672
      - MessageBus=RabbitMq
      - CosmosDbEndpoint=${DevelopCosmosDbEndpoint}
      - SECRET_kv_k00021_test_AzureServiceBusConnectionString=${AzureServiceBusConnectionString}
      
  pondres.omnia.ingestion.internalapi:
    image: ${DOCKER_REGISTRY-}pondresomniaingestioninternalapi
    build:
      context: ..
      dockerfile: src/Pondres.Omnia.Ingestion.Api.Internal/Dockerfile
    environment:
      - RabbitConnectionString=amqp://guest:guest@rabbitmq:5672
      - MessageBus=RabbitMq
      - CosmosDbEndpoint=${DevelopCosmosDbEndpoint}


  pondres.omnia.ingestion.externalapi:
    image: ${DOCKER_REGISTRY-}pondresomniaingestionexternalapi
    build:
      context: ..
      dockerfile: src/Pondres.Omnia.Ingestion.Api.External/Dockerfile
    environment:
      - RabbitConnectionString=amqp://guest:guest@rabbitmq:5672
      - MessageBus=RabbitMq
      - CosmosDbEndpoint=${DevelopCosmosDbEndpoint}
      
  pondres.omnia.ingestion.sendgrid:
    image: ${DOCKER_REGISTRY-}pondresomniaingestionsendgrid
    build:
      context: ..
      dockerfile: src/Pondres.Omnia.Ingestion.SendGrid/Dockerfile
    environment:
      - RabbitConnectionString=amqp://guest:guest@rabbitmq:5672
      - MessageBus=RabbitMq
      - CosmosDbEndpoint=${DevelopCosmosDbEndpoint}

  pondres.omnia.ingestion.shopify:
    image: ${DOCKER_REGISTRY-}pondresomniaingestionshopify
    build:
      context: ..
      dockerfile: src/Pondres.Omnia.Ingestion.Shopify/Dockerfile
    environment:
      - RabbitConnectionString=amqp://guest:guest@rabbitmq:5672
      - MessageBus=RabbitMq
      - CosmosDbEndpoint=${DevelopCosmosDbEndpoint}

  rabbitmq:
    image: masstransit/rabbitmq
    hostname: ingestion-rabbit
    ports: 
     - "15672:15672"
     - "5672:5672"
    environment:
     - RABBITMQ_ERLANG_COOKIE=456dfgh34sdfb
     
  azurite:
    image: mcr.microsoft.com/azure-storage/azurite
    ports:
     - "10000:10000"
     - "10001:10001"



