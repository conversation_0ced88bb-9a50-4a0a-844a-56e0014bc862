﻿using Pondres.Omnia.Ingestion.Api.Internal.Config;
using Pondres.Omnia.Ingestion.Common.Extensions;
using Pondres.Omnia.Ingestion.Common.Handlers;
using Pondres.Omnia.Ingestion.Common.Service;
using Solude.ApiBase.Extensions;

namespace Pondres.Omnia.Ingestion.Api.Internal;

public class Startup(IHostEnvironment environment, IConfiguration configuration)
{
    private readonly InternalApiAppSettings appSettings = configuration.Get<InternalApiAppSettings>() ??
        throw new InvalidOperationException("AppSettings not bindable");

    public void Configure(IApplicationBuilder app)
    {
        app.UseDefaultApiDependencies(Program.Name, appSettings);
    }

    public void ConfigureServices(IServiceCollection services)
    {
        services.AddDefaultApiDependencies(appSettings, Program.Name, auth => auth.RequiredToken = appSettings.InternalApiIngestionAuthToken);

        services.AddCommonDependencies(environment, appSettings);

        services.AddTransient<CreateOrderCommandHandler>();
    }
}