﻿using Pondres.Omnia.Ingestion.Shopify.Services;
using Pondres.Omnia.Ingestion.Tests.Fixtures;
using Pondres.Omnia.Ingestion.Tests.Fixtures.Data;
using Pondres.Omnia.Ingestion.V2.Contracts.Events;
using Pondres.Omnia.OrderHub.Contracts.Order;
using Xunit;
using static Grpc.Core.Metadata;

namespace Pondres.Omnia.Ingestion.Tests.IntegrationTests.Ingestion;

[Collection("IntegrationTests")]
public class ShopifyIngestionTests(IntegrationTestFixture fixture) : BaseIntegrationTest(fixture)
{
    [Fact]
    public async Task PerformBackgroundCheck_ValidRequest_CreatesOrder()
    {
        // Arrange
        var customer = GetCustomer();
        var configuration = await fixture.Configuration.CreateShopifyConfigurationAsync(customer);
        var customerResponse = fixture.ShopifyApp.CustomerRequestSender.SetupSuccessCustomerResponse(customer);

        var apiUri = SecretsFixture.RegisterSecret(
             vaultName: customerResponse.Customer.VaultName,
             secretKey: configuration.ShopifyUrlSecretName,
             secretValue: $"https://{Guid.NewGuid()}");

        SecretsFixture.RegisterSecret(
            vaultName: customerResponse.Customer.VaultName,
            secretKey: configuration.ShopifyAccessTokenSecretName,
            secretValue: Guid.NewGuid().ToString());

        fixture.ShopifyApp.ShopifyApiFixture
            .SetupHttpClientFactoryHandler()
            .SetupShopifyOrders(apiUri);

        var service = fixture.ResolveShopifyService<ShopifyOrderFetchBackgroundService>();

        // Act
        await service.FetchAndImportOrdersAsync(customer);

        // Assert
        fixture.ShopifyApp.BusPublisherObserver.Should().NotHaveSeenMessage<IngestionFailure>(x => x.Customer == customer);
        fixture.ShopifyApp.BusPublisherObserver.Should().HaveSeenMessage<CreateOrderMessage>(x => x.Customer == customer);
    }
}
