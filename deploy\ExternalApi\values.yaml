# generic api template

replicaCount: 1

namespace: omnia

image:
  repository: "#|ContainerRegistry|#.azurecr.io/omnia-ingestion-externalapi"
  pullPolicy: IfNotPresent
  tag: "#|Build.BuildNumber|#"

imagePullSecrets: []
nameOverride: "ingestion-externalapi" # name of application
fullnameOverride: "ingestion-externalapi" # name of application

config:
  RELEASE_NAME: "#|Release.ReleaseName|##|Release.AttemptNumber|#"
  TZ: "Europe/Amsterdam"
  VaultName: "#|AZURE_KEYVAULT_NAME|#"
  ASPNETCORE_ENVIRONMENT: "#|ASPNETCORE_ENVIRONMENT|#"

secrets:
  AppInsightsConnectionString: "#|AppInsightsConnectionString|#"

podAnnotations: {}
podLabels:
  aadpodidbinding: "#|AKS_IDENTITY_NAME|#"

service:
  type: ClusterIP
  port: 80
  containerPort: 8080

probes:
  initialDelaySeconds: 0
  periodSeconds: 10
  timeoutSeconds: 2
  successThreshold: 1
  failureThreshold: 5
  http:
    livenessPath: /health/live
    readinessPath: /health/ready
    scheme: HTTP

hostAliases: []
#  - ip: IP_ADDRESS_1
#    hostnames:
#      - HOST_NAME_1

ingress:
  enabled: true # enable if API needs to be accesible from outside the cluster
  className: "nginx-external"
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
  hosts:
    - host: "#|Omnia.BaseApiDomain|#"
      paths:
        - path: "/ingestion/api"
          pathType: Prefix
  tls:
    - secretName: ingress-tls-csi
      hosts:
        - "#|Omnia.BaseApiDomain|#"

resources:
  limits:
    cpu: 250m
    memory: 256Mi
  requests:
    cpu: 100m 
    memory: 256Mi

volumes:
  - name: secrets-store-inline
    csi:
      driver: secrets-store.csi.k8s.io
      readOnly: true
      volumeAttributes:
        secretProviderClass: "azure-tls"
  - name: tmp
    emptyDir: {}

volumeMounts:
  - name: secrets-store-inline
    mountPath: "/mnt/secrets-store"
    readOnly: true 
  - name: tmp
    mountPath: /tmp

nodeSelector: {}

tolerations: []

affinity: {}
