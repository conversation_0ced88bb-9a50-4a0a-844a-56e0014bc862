﻿using Azure.Identity;
using Azure.Security.KeyVault.Secrets;
using Microsoft.Extensions.Caching.Memory;
using Serilog;
using System;
using System.Threading.Tasks;

namespace Pondres.Omnia.Ingestion.Common.Provider;

public class KeyVaultSecretProvider(IMemoryCache memoryCache) : ISecretProvider
{
    private readonly DefaultAzureCredential defaultAzureCredential = new();

    public async Task<string?> GetSecretAsync(string vaultName, string secretName, bool ignoreCache = false)
    {
        if (!ignoreCache && memoryCache.TryGetValue(GetCacheKey(vaultName, secretName), out string? secretValue))
            return secretValue;

        Log.Debug("Retrieving secret {SecretName} from vault {VaultName}", secretName);

        var secretClient = GetSecretClient(vaultName);

        var response = await secretClient.GetSecretAsync(secretName);

        UpdateSecretCache(vaultName, secretName, response.Value.Value);

        return response.Value.Value;
    }

    private SecretClient CreateSecretClient(string vaultName) =>
        new(new Uri($"https://{vaultName}.vault.azure.net"), defaultAzureCredential);

    private static string GetCacheKey(string vaultName, string secretName) => $"kvsecret_{vaultName}_{secretName}";

    private SecretClient GetSecretClient(string vaultName)
    {
        if (memoryCache.TryGetValue($"secretclient_{vaultName}", out SecretClient? secretClient) && secretClient != null)
        {
            Log.Debug("Retrieved secretclient {VaultName} from cache", vaultName);

            return secretClient;
        }

        Log.Debug("Resolving secret client {VaultName}", vaultName);

        secretClient = CreateSecretClient(vaultName);

        memoryCache.Set(vaultName, secretClient, new MemoryCacheEntryOptions
        {
            SlidingExpiration = TimeSpan.FromSeconds(10)
        });

        return secretClient;
    }

    private void UpdateSecretCache(string vaultName, string secretName, string value)
    {
        memoryCache.Set(GetCacheKey(vaultName, secretName), value, new MemoryCacheEntryOptions
        {
            SlidingExpiration = TimeSpan.FromSeconds(10)
        });
    }
}
