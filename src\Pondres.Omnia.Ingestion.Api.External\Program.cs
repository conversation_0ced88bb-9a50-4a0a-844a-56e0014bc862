using Serilog;
using Solude.ApiBase.Configuration;
using Solude.ApiBase.Contracts;
using Solude.ApiBase.Extensions;

namespace Pondres.Omnia.Ingestion.Api.External;

public class Program
{
    protected Program() { }

    public const string Name = "ExternalApi";

    public static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .ConfigureAppConfiguration((_, config) => config.AddDefaultServiceKeyVaultIfAvailable())
            .ConfigureWebHostDefaults(webBuilder => webBuilder.UseStartup<Startup>())
            .UseSerilog();

    public static async Task<int> Main(string[] args)
    {
        LoggerBuilder.BuildLogger(Name);

        try
        {
            Log.Information("Building host");

            var host = CreateHostBuilder(args).Build();

            var startupTasks = host.Services.GetServices<IStartupTask>();
            foreach (var startupTask in startupTasks)
                await startupTask.ExecuteAsync();

            Log.Information("Running host");

            await host.RunAsync();

            return 0;
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "Host terminated unexpectedly");
            return 1;
        }
        finally
        {
            await Log.CloseAndFlushAsync();
        }
    }
}

