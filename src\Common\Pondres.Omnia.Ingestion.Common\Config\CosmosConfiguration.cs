﻿namespace Pondres.Omnia.Ingestion.Common.Config;

public static class CosmosConfiguration
{
    public static string ConfigurationContainerPartitionKey => "/type";
    public static string ConfigurationContainerName => "Configuration";

    public static string ConfigurationAwsQueueTypeName => "AwsQueueRegistrations";
    public static string ConfigurationAzureServiceBusQueueTypeName => "AzureServiceBusQueueRegistrations";

    public static string ConfigurationShopifyEnvironmentTypeName => "ShopifyEnvironmentRegistrations";

    public static string IngestionDatabaseName => "Ingestion";
}
