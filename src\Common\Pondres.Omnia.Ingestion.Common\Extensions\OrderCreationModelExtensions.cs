﻿using Newtonsoft.Json;
using Pondres.Omnia.Ingestion.Common.Model;
using Pondres.Omnia.Ingestion.Common.Storage;
using Pondres.Omnia.Ingestion.V2.Contracts.Events;
using Pondres.Omnia.OrderHub.Contracts.Order;
using System;

namespace Pondres.Omnia.Ingestion.Common.Extensions;

public static class OrderCreationModelExtensions
{
    public static string ToRawDataFolderPath(this OrderCreationModel model) =>
        $"{model.FlowName}/{model.OrderId}/{DataStorageConstants.RawDataFolderName}";

    public static CreateOrderMessage ToCreateOrderMessage(this OrderCreationModel model, string storageContainerName) =>
        new()
        {
            OrderId = model.OrderId,
            CustomerReference = model.CustomerReference,
            Source = model.SourceName,
            Flow = model.FlowName,
            FlowId = model.FlowId,
            RequestId = model.RequestId,
            DataContainerName = storageContainerName,
            Customer = model.Customer,
            Categories = new OrderCategories
            {
                One = model.Categories.One,
                Two = model.Categories.Two,
                Three = model.Categories.Three
            }
        };

    public static IngestionDataReceived ToDataReceivedEvent(this OrderCreationModel model) =>
        new()
        {
            Customer = model.Customer,
            Source = model.SourceName,
            Flow = model.FlowName,
            Timestamp = DateTimeOffset.Now,
            RequestId = model.RequestId,
            CustomerReference = model.CustomerReference
        };

    public static IngestionDataSaved ToDataSavedEvent(this OrderCreationModel model, string storageContainerName) =>
        new()
        {
            Customer = model.Customer,
            Source = model.SourceName,
            FileName = model.FileName,
            Flow = model.FlowName,
            Timestamp = DateTimeOffset.Now,
            RequestId = model.RequestId,
            DataContainerName = storageContainerName,
            CustomerReference = model.CustomerReference
        };

    public static IngestionDataSaveFailed ToDataSaveFailedEvent(this OrderCreationModel model, Exception exception, string storageContainerName) =>
        new()
        {
            RequestId = model.RequestId,
            Source = model.SourceName,
            Customer = model.Customer,
            FileName = model.FileName,
            Message = exception.Message,
            Timestamp = DateTimeOffset.Now,
            Flow = model.FlowName,
            DataContainerName = storageContainerName,
            CustomerReference = model.CustomerReference
        };

    public static IngestionFailure ToIngestionFailure(this OrderCreationModel orderCreationModel, Exception exception) =>
        new()
        {
            Message = $"{exception.Message}||{exception.InnerException?.Message}",
            ExceptionData = JsonConvert.SerializeObject(exception),
            Customer = orderCreationModel.Customer,
            Flow = orderCreationModel.FlowName,
            RequestId = orderCreationModel.RequestId,
            Timestamp = DateTimeOffset.Now
        };

    public static IngestionValidationFailed ToIngestionValidationFailed(this OrderCreationModel orderCreationModel, Exception exception) =>
        new()
        {
            Message = $"{exception.Message}||{exception.InnerException?.Message}",
            ExceptionData = JsonConvert.SerializeObject(exception),
            Customer = orderCreationModel.Customer,
            Flow = orderCreationModel.FlowName,
            RequestId = orderCreationModel.RequestId,
            Timestamp = DateTimeOffset.Now
        };
}