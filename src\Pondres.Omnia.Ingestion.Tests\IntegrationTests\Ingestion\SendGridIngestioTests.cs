﻿using FluentAssertions;
using MassTransit.Internals;
using MassTransit.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Pondres.Omnia.Ingestion.SendGrid.Models;
using Pondres.Omnia.Ingestion.Tests.Fixtures;
using Pondres.Omnia.Ingestion.Tests.Helper;
using Pondres.Omnia.OrderHub.Contracts.Order;
using System.Net.Mime;
using System.Text;
using Xunit;

namespace Pondres.Omnia.Ingestion.Tests.IntegrationTests.Ingestion;

[Collection("IntegrationTests")]
public class SendGridIngestioTests(IntegrationTestFixture fixture) : BaseIntegrationTest(fixture)
{
    [Fact]
    public async Task IngestSendGridTriggerFileOrderAsync_ValidOrder_OrderCreated()
    {
        // Arrange
        var customer = GetCustomer();
        var containerName = $"{customer}-container".ToLower();
        var customerReference = Guid.NewGuid().ToString();
        var flow = Guid.NewGuid().ToString();
        var triggerFileName = $"{customerReference}.finish";

        fixture.SendGridApp.CustomerRequestSender.SetupSuccessCustomerResponse(customer);

        var message = new SendGridBlobCreatedEvent()
        {
            Id = Guid.NewGuid().ToString(),
            Subject = $"/blobServices/default/containers/{containerName}/blobs/{triggerFileName}"
        };

        var sourceContainer = await fixture.Blob.GetOrCreateContainerAsync(containerName);

        var stream = new MemoryStream(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(new SampleOrderData(customerReference))));

        await sourceContainer.UploadBlobAsync(triggerFileName, Stream.Null);

        await sourceContainer.UploadBlobAsync("file1", stream);
        stream.Seek(0, SeekOrigin.Begin);

        await sourceContainer.UploadBlobAsync("file2", stream);
        stream.Seek(0, SeekOrigin.Begin);

        // Act
        await fixture.SendGridApp.SendAndWaitForConsumeAsync(
            message: message,
            queue: "ingestionservice_sendgrid_bulk",
            matchFunc: x => x.Id == message.Id,
            configureSend: configure =>
            {
                configure.Headers.Set("x-customer", customer);
                configure.Headers.Set("x-flow", flow);

                // Makes the json message raw
                configure.Serializer = new NewtonsoftRawJsonBodyMessageSerializer(
                    JToken.FromObject(configure.Message),
                    new ContentType("application/json"),
                    new MassTransit.RawSerializerOptions());
            });

        // Assert
        var orderCreated = fixture.SendGridApp.BusPublisherObserver.Should().HaveSeenMessage<CreateOrderMessage>(x => x.Customer == customer);

        var orderDataContainer = await fixture.Blob.GetOrCreateContainerAsync("orderdata");
        var orderFiles = await orderDataContainer.GetBlobsAsync(prefix: flow).ToListAsync();
        orderFiles.Should().HaveCount(2);

        var processedFilesContainer = await fixture.Blob.GetOrCreateContainerAsync($"{containerName}-processed");
        var processedFiles = await processedFilesContainer.GetBlobsAsync().ToListAsync();
        processedFiles.Should().HaveCount(3);

        var sourceContainerFiles = await sourceContainer.GetBlobsAsync().ToListAsync();
        sourceContainerFiles.Should().HaveCount(0);
    }

    [Fact]
    public async Task IngestSendGridTriggerFileOrderAsync_ValidOrder_SubFolder_OrderCreated()
    {
        // Arrange
        var customer = GetCustomer();
        var containerName = $"{customer}-container".ToLower();
        var customerReference = Guid.NewGuid().ToString();
        var flow = Guid.NewGuid().ToString();
        var triggerFileName = $"{customerReference}.finish";

        fixture.SendGridApp.CustomerRequestSender.SetupSuccessCustomerResponse(customer);

        var message = new SendGridBlobCreatedEvent()
        {
            Id = Guid.NewGuid().ToString(),
            Subject = $"/blobServices/default/containers/{containerName}/blobs/{customerReference}/{triggerFileName}"
        };

        var sourceContainer = await fixture.Blob.GetOrCreateContainerAsync(containerName);

        var stream = new MemoryStream(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(new SampleOrderData(customerReference))));

        await sourceContainer.UploadBlobAsync($"{customerReference}/{triggerFileName}", stream);
        stream.Seek(0, SeekOrigin.Begin);

        await sourceContainer.UploadBlobAsync($"{customerReference}/file1", stream);
        stream.Seek(0, SeekOrigin.Begin);

        await sourceContainer.UploadBlobAsync($"file2", stream); // Should not be part of the final counts
        stream.Seek(0, SeekOrigin.Begin);

        // Act
        await fixture.SendGridApp.SendAndWaitForConsumeAsync(
            message: message,
            queue: "ingestionservice_sendgrid_bulk",
            matchFunc: x => x.Id == message.Id,
            configureSend: configure =>
            {
                configure.Headers.Set("x-customer", customer);
                configure.Headers.Set("x-flow", flow);

                // Makes the json message raw
                configure.Serializer = new NewtonsoftRawJsonBodyMessageSerializer(
                    JToken.FromObject(configure.Message),
                    new ContentType("application/json"),
                    new MassTransit.RawSerializerOptions());
            });

        // Assert
        var orderCreated = fixture.SendGridApp.BusPublisherObserver.Should().HaveSeenMessage<CreateOrderMessage>(x => x.Customer == customer);

        var orderDataContainer = await fixture.Blob.GetOrCreateContainerAsync("orderdata");

        var allFiles = await orderDataContainer.GetBlobsAsync().ToListAsync();

        var orderFiles = await orderDataContainer.GetBlobsAsync(prefix: flow).ToListAsync();
        orderFiles.Should().HaveCount(1);

        var processedFilesContainer = await fixture.Blob.GetOrCreateContainerAsync($"{containerName}-processed");
        var processedFiles = await processedFilesContainer.GetBlobsAsync().ToListAsync();
        processedFiles.Should().HaveCount(2);

        var sourceContainerFiles = await sourceContainer.GetBlobsAsync().ToListAsync();
        sourceContainerFiles.Should().HaveCount(1);
        sourceContainerFiles.Single().Name.Should().EndWith("file2");
    }
}
