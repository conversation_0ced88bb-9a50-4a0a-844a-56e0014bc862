﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Pondres.Omnia.Ingestion.Common.Model;

namespace Pondres.Omnia.Ingestion.Amazon.Entities;

public class AwsCustomerQueueRegistrationEntity
{
    [JsonProperty("queueUrl")]
    public string QueueUrl { get; set; }

    [JsonProperty("flowName")]
    public string FlowName { get; set; }

    [JsonProperty("accessKeySecretName")]
    public string AccessKeySecretName { get; set; }

    [JsonProperty("secretKeySecretName")]
    public string SecretKeySecretName { get; set; }

    [JsonProperty("customerReferenceDeterminationType")]
    [JsonConverter(typeof(StringEnumConverter))]
    public CustomerReferenceDeterminationType CustomerReferenceDeterminationType { get; set; }

    [JsonProperty("customerReferenceDeterminationTypeValue")]
    public string CustomerReferenceDeterminationTypeValue { get; set; }

    public AwsCustomerQueueRegistrationEntity(
        string queueUrl,
        string flowName,
        string accessKeySecretName,
        string secretKeySecretName,
        CustomerReferenceDeterminationType customerReferenceDeterminationType,
        string customerReferenceDeterminationTypeValue)
    {
        QueueUrl = queueUrl;
        FlowName = flowName;
        AccessKeySecretName = accessKeySecretName;
        SecretKeySecretName = secretKeySecretName;
        CustomerReferenceDeterminationType = customerReferenceDeterminationType;
        CustomerReferenceDeterminationTypeValue = customerReferenceDeterminationTypeValue;
    }
}
