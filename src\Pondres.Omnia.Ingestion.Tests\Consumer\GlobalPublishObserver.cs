﻿using FluentAssertions;
using FluentAssertions.Execution;
using FluentAssertions.Primitives;
using MassTransit;
using System.Collections.Concurrent;
using System.Linq.Expressions;

namespace Pondres.Omnia.Ingestion.Tests.Consumer;

public class GlobalPublishObserver : IPublishObserver
{
    public ConcurrentBag<object> PublishedMessages { get; } = [];

    public ConcurrentBag<object> FaultedMessages { get; } = [];

    public Task PostPublish<T>(PublishContext<T> context) where T : class
    {
        PublishedMessages.Add(context.Message);
        return Task.CompletedTask;
    }

    public Task PrePublish<T>(PublishContext<T> context) where T : class
    {
        return Task.CompletedTask;
    }

    public Task PublishFault<T>(PublishContext<T> context, Exception exception) where T : class
    {
        FaultedMessages.Add(context.Message);
        return Task.CompletedTask;
    }

    public TestPublishObserverAssertions Should()
    {
        return new TestPublishObserverAssertions(this);
    }
}

public class TestPublishObserverAssertions(GlobalPublishObserver instance) :
    ReferenceTypeAssertions<GlobalPublishObserver, TestPublishObserverAssertions>(instance)
{
    protected override string Identifier => "publisher";

    public List<TMessage> HaveSeenMessages<TMessage>(
        Expression<Func<TMessage, bool>> messageExpression, int expectedCount, string because = "", params object[] becauseArgs)
    {
        var messageMatchFunc = messageExpression.Compile();

        var matchedMessages = Subject.PublishedMessages.OfType<TMessage>().Where(messageMatchFunc).ToList();

        Execute.Assertion
            .BecauseOf(because, becauseArgs)
            .ForCondition(matchedMessages.Count == expectedCount)
            .FailWith("Found {0} messages where {1}{reason}, but expected {2}." + Environment.NewLine + Environment.NewLine + "{3}.",
                matchedMessages.Count, messageExpression, expectedCount, Subject);

        return matchedMessages;
    }

    public TMessage HaveSeenMessage<TMessage>(
        Expression<Func<TMessage, bool>> messageExpression, string because = "", params object[] becauseArgs)
    {
        var messageMatchFunc = messageExpression.Compile();
        var messages = Subject.PublishedMessages.OfType<TMessage>().Where(messageMatchFunc);

        messages.Should().ContainSingle();

        var matchedMessage = messages.Single();

        Execute.Assertion
            .BecauseOf(because, becauseArgs)
            .ForCondition(matchedMessage != null)
            .FailWith("Expected message was not published where {0}{reason}, but the condition was not met by:"
                        + Environment.NewLine + Environment.NewLine + "{1}.",
                messageExpression, Subject);

        return matchedMessage!;
    }
    public TMessage NotHaveSeenMessage<TMessage>(
        Expression<Func<TMessage, bool>> messageExpression, string because = "", params object[] becauseArgs)
    {
        var messageMatchFunc = messageExpression.Compile();

        var matchedMessage = Subject.PublishedMessages.OfType<TMessage>().SingleOrDefault(messageMatchFunc);

        Execute.Assertion
            .BecauseOf(because, becauseArgs)
            .ForCondition(matchedMessage == null)
            .FailWith("Expected message was published where {0}{reason}, but the condition was not met by:"
                        + Environment.NewLine + Environment.NewLine + "{1}.",
                messageExpression, Subject);

        return matchedMessage!;
    }
}
