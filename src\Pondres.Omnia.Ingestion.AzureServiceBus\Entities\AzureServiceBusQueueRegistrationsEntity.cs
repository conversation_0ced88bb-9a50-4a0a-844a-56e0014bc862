﻿using Newtonsoft.Json;
using Pondres.Omnia.Ingestion.Common.Config;

namespace Pondres.Omnia.Ingestion.AzureServiceBus.Entities;

public class AzureServiceBusQueueRegistrationsEntity
{
    public AzureServiceBusQueueRegistrationsEntity(string customer, string vaultName, string storageAccountName)
    {
        Customer = customer;
        VaultName = vaultName;
        StorageAccountName = storageAccountName;
    }

    [JsonProperty("id")]
    public string Customer { get; set; }

    [JsonProperty("vaultName")]
    public string VaultName { get; set; }

    [JsonProperty("storageAccountName")]
    public string StorageAccountName { get; set; } 

    [JsonProperty("active")]
    public bool Active { get; set; } = true;

    [JsonProperty("createdUtc")]
    public DateTime CreatedUtc { get; set; } = DateTime.UtcNow;

    [JsonProperty("updatedUtc")]
    public DateTime UpdatedUtc { get; set; } = DateTime.UtcNow;

    [JsonProperty("lastSyncUtc")]
    public DateTime? LastSyncUtc { get; set; }

    [JsonProperty("type")]
    public string Type { get; } = CosmosConfiguration.ConfigurationAzureServiceBusQueueTypeName;

    [JsonProperty("version")]
    public string Version { get; set; } = "1";

    [JsonProperty("queues")]
    public List<AzureServiceBusCustomerQueueRegistrationEntity> Queues { get; set; } = [];

}
