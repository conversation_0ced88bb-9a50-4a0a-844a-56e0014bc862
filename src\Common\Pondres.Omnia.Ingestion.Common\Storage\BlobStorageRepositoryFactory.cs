﻿using Azure.Core;
using Azure.Identity;
using Azure.Storage.Blobs;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Pondres.Omnia.Ingestion.Common.Storage;

public class DataStorageOptions
{
    public bool IsDevelopment { get; set; }
    public string DevelopmentConnectionString { get; set; } = string.Empty;
}

public class BlobStorageRepositoryFactory(IOptions<DataStorageOptions> storageOptions) : IBlobStorageRepositoryFactory
{
    private readonly DefaultAzureCredential azureCredential = new();

    private readonly SemaphoreSlim clientAddSemaphore = new(1, 1);
    private readonly Dictionary<string, IBlobStorageRepository> repositories = [];
    private readonly DataStorageOptions storageOptions = storageOptions.Value;

    public async Task<IBlobStorageRepository> GetStorageRepositoryAsync(string storageAccountName)
    {
        var serviceUri = storageOptions.IsDevelopment ?
            storageOptions.DevelopmentConnectionString :
            $"https://{storageAccountName}.blob.core.windows.net";

        var repository = await GetRepositoryForUriAsync(serviceUri, storageOptions.IsDevelopment);

        return repository;
    }

    private static BlobStorageRepository CreateDevelopmentRepository(string serviceUri, BlobClientOptions options) =>
        new(new BlobServiceClient(
                connectionString: serviceUri,
                options: options),
            blobUri => new BlobClient(blobUri, DataStorageConstants.DevelopmentAccountSharedKey));

    private BlobStorageRepository CreateAzureRepository(Uri serviceUri, BlobClientOptions options) =>
        new(new BlobServiceClient(
                serviceUri: serviceUri,
                credential: azureCredential,
                options: options),
            blobUri => new BlobClient(blobUri, azureCredential));

    private BlobStorageRepository CreateNewRepository(string serviceUri, bool isDevelopment)
    {
        var options = new BlobClientOptions();
        options.Retry.MaxRetries = 3;
        options.Retry.Mode = RetryMode.Exponential;

        return isDevelopment ? CreateDevelopmentRepository(serviceUri, options) : CreateAzureRepository(new Uri(serviceUri), options);
    }

    private async Task<IBlobStorageRepository> GetRepositoryForUriAsync(string serviceUri, bool isDevelopment)
    {
        if (repositories.TryGetValue(serviceUri, out var repository))
        {
            return repository;
        }
        else
        {
            await clientAddSemaphore.WaitAsync();

            try
            {
                // Maybe it was created in the meantime while waiting.
                if (repositories.TryGetValue(serviceUri, out var createdRepository))
                {
                    return createdRepository;
                }
                else
                {
                    var newRepository = CreateNewRepository(serviceUri, isDevelopment);
                    repositories.Add(serviceUri, newRepository);
                    return newRepository;
                }
            }
            finally
            {
                clientAddSemaphore.Release();
            }
        }
    }
}