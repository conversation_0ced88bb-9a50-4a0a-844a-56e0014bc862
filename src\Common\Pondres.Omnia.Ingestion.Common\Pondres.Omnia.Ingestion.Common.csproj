﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.8.0" />
		<PackageReference Include="Azure.Storage.Blobs" Version="12.25.0" />
		<PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.4.0" />
		<PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.8" />
		<PackageReference Include="Microsoft.Azure.Cosmos" Version="3.52.1" />
		<PackageReference Include="Solude.ApiBase" Version="1.20250714.4" />
		<PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />
		<PackageReference Include="MassTransit" Version="8.5.1" />
		<PackageReference Include="MassTransit.WebJobs.ServiceBus" Version="8.5.1" />
		<PackageReference Include="MassTransit.RabbitMQ" Version="8.5.1" />
		<PackageReference Include="MassTransit.Azure.ServiceBus.Core" Version="8.5.1" />
		<PackageReference Include="MassTransit.Newtonsoft" Version="8.5.1" />
		<PackageReference Include="Serilog" Version="4.3.0" />
		<PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
		<PackageReference Include="Serilog.Sinks.ApplicationInsights" Version="4.0.0" />
		<PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
		<PackageReference Include="Polly" Version="8.6.2" />
		<PackageReference Include="FluentValidation" Version="12.0.0" />
		<PackageReference Include="Pondres.Omnia.OrderHub.Contracts" Version="3.20250805.2" />
		<PackageReference Include="Pondres.Omnia.Customer.Contracts" Version="1.20250804.1" />
		<PackageReference Include="Solude.CustomerProvider" Version="1.20250714.4" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Pondres.Omnia.Ingestion.V2.Contracts\Pondres.Omnia.Ingestion.V2.Contracts.csproj" />
	</ItemGroup>

</Project>
