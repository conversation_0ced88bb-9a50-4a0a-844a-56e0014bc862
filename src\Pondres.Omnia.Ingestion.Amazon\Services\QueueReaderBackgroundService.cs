﻿using Pondres.Omnia.Ingestion.Amazon.Commands;
using Pondres.Omnia.Ingestion.Amazon.Queues;
using Serilog;

namespace Pondres.Omnia.Ingestion.Amazon.Services;

public class QueueReaderBackgroundService(
    QueueQueries queries,
    ReadAwsQueueCommandHandler queueCommandHandler) : BackgroundService
{
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        using var timer = new PeriodicTimer(TimeSpan.FromMinutes(1));

        do
        {
            Log.Information("Reading queues");

            var queueRegistrations = await queries.GetActiveQueuesAsync();

            Log.Information("Found {QueueCount} queues to read", queueRegistrations.Count);

            foreach (var registration in queueRegistrations)
                foreach (var queue in registration.Queues)
                {
                    Log.Information("Reading queue {QueueName}", queue.QueueUrl);

                    var command = new ReadAwsQueueCommand(
                        QueueUrl: queue.QueueUrl,
                        Customer: registration.Customer,
                        CustomerVaultName: registration.VaultName,
                        CustomerStorageAccountName: registration.StorageAccountName,
                        FlowName: queue.FlowName,
                        AccessKeySecretName: queue.AccessKeySecretName,
                        SecretKeySecretName: queue.SecretKeySecretName,
                        CustomerReferenceDeterminationTypeValue: queue.CustomerReferenceDeterminationTypeValue,
                        CustomerReferenceDeterminationType: queue.CustomerReferenceDeterminationType);

                    var result = await queueCommandHandler.HandleAsync(command);

                    if (result.IsFailed)
                        Log.Error("Error while reading queue: {Reasons}", result.Reasons);
                    else
                        Log.Information("Queue {QueueName} read successfully", queue.QueueUrl);
                }
        } while (!stoppingToken.IsCancellationRequested && await timer.WaitForNextTickAsync(stoppingToken));
    }
}
