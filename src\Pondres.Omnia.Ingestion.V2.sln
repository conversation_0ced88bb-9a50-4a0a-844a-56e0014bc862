﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31825.309
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Ingestion.Common", "Common\Pondres.Omnia.Ingestion.Common\Pondres.Omnia.Ingestion.Common.csproj", "{0D984D08-9AB6-42AA-82D9-D0BED8C215B5}"
EndProject
Project("{E53339B2-1760-4266-BCC7-CA923CBCF16C}") = "docker-compose", "docker-compose.dcproj", "{965FBD60-FB07-4A3F-9EEE-A22EEF41500E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{63206ADB-ED95-4D29-883E-064E5BC591D5}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Ingestions", "Ingestions", "{751B6494-C20C-41C9-9B84-2C73B49423F2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Ingestion.Amazon", "Pondres.Omnia.Ingestion.Amazon\Pondres.Omnia.Ingestion.Amazon.csproj", "{F46FF648-8B7A-4539-BFB4-42D99B902417}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Ingestion.V2.Contracts", "Common\Pondres.Omnia.Ingestion.V2.Contracts\Pondres.Omnia.Ingestion.V2.Contracts.csproj", "{6AE123B0-FFEF-41F5-A094-CE8B016C1590}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Ingestion.AzureServiceBus", "Pondres.Omnia.Ingestion.AzureServiceBus\Pondres.Omnia.Ingestion.AzureServiceBus.csproj", "{3B43E09B-53C4-40E2-9D0B-4B13E2FB5BD2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Ingestion.Tests", "Pondres.Omnia.Ingestion.Tests\Pondres.Omnia.Ingestion.Tests.csproj", "{C040ACC2-8410-400B-BA10-95081E869314}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Pondres.Omnia.Ingestion.Api.Internal", "Pondres.Omnia.Ingestion.Api.Internal\Pondres.Omnia.Ingestion.Api.Internal.csproj", "{B9874010-B2F7-4D8E-A9E2-98560CB8640D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Pondres.Omnia.Ingestion.SendGrid", "Pondres.Omnia.Ingestion.SendGrid\Pondres.Omnia.Ingestion.SendGrid.csproj", "{33168D8D-8995-40D7-97B7-7C14FD66F0F9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Pondres.Omnia.Ingestion.Shopify", "Pondres.Omnia.Ingestion.Shopify\Pondres.Omnia.Ingestion.Shopify.csproj", "{000F071F-7790-4A58-AED3-0152F31C3FF8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Pondres.Omnia.Ingestion.Api.External", "Pondres.Omnia.Ingestion.Api.External\Pondres.Omnia.Ingestion.Api.External.csproj", "{0829FADB-C71A-4F2C-8594-C7BE339F9075}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{0D984D08-9AB6-42AA-82D9-D0BED8C215B5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0D984D08-9AB6-42AA-82D9-D0BED8C215B5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0D984D08-9AB6-42AA-82D9-D0BED8C215B5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0D984D08-9AB6-42AA-82D9-D0BED8C215B5}.Release|Any CPU.Build.0 = Release|Any CPU
		{965FBD60-FB07-4A3F-9EEE-A22EEF41500E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{965FBD60-FB07-4A3F-9EEE-A22EEF41500E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{965FBD60-FB07-4A3F-9EEE-A22EEF41500E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{965FBD60-FB07-4A3F-9EEE-A22EEF41500E}.Release|Any CPU.Build.0 = Release|Any CPU
		{F46FF648-8B7A-4539-BFB4-42D99B902417}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F46FF648-8B7A-4539-BFB4-42D99B902417}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F46FF648-8B7A-4539-BFB4-42D99B902417}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F46FF648-8B7A-4539-BFB4-42D99B902417}.Release|Any CPU.Build.0 = Release|Any CPU
		{6AE123B0-FFEF-41F5-A094-CE8B016C1590}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6AE123B0-FFEF-41F5-A094-CE8B016C1590}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6AE123B0-FFEF-41F5-A094-CE8B016C1590}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6AE123B0-FFEF-41F5-A094-CE8B016C1590}.Release|Any CPU.Build.0 = Release|Any CPU
		{3B43E09B-53C4-40E2-9D0B-4B13E2FB5BD2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3B43E09B-53C4-40E2-9D0B-4B13E2FB5BD2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3B43E09B-53C4-40E2-9D0B-4B13E2FB5BD2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3B43E09B-53C4-40E2-9D0B-4B13E2FB5BD2}.Release|Any CPU.Build.0 = Release|Any CPU
		{C040ACC2-8410-400B-BA10-95081E869314}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C040ACC2-8410-400B-BA10-95081E869314}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C040ACC2-8410-400B-BA10-95081E869314}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C040ACC2-8410-400B-BA10-95081E869314}.Release|Any CPU.Build.0 = Release|Any CPU
		{B9874010-B2F7-4D8E-A9E2-98560CB8640D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B9874010-B2F7-4D8E-A9E2-98560CB8640D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B9874010-B2F7-4D8E-A9E2-98560CB8640D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B9874010-B2F7-4D8E-A9E2-98560CB8640D}.Release|Any CPU.Build.0 = Release|Any CPU
		{33168D8D-8995-40D7-97B7-7C14FD66F0F9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{33168D8D-8995-40D7-97B7-7C14FD66F0F9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{33168D8D-8995-40D7-97B7-7C14FD66F0F9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{33168D8D-8995-40D7-97B7-7C14FD66F0F9}.Release|Any CPU.Build.0 = Release|Any CPU
		{000F071F-7790-4A58-AED3-0152F31C3FF8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{000F071F-7790-4A58-AED3-0152F31C3FF8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{000F071F-7790-4A58-AED3-0152F31C3FF8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{000F071F-7790-4A58-AED3-0152F31C3FF8}.Release|Any CPU.Build.0 = Release|Any CPU
		{0829FADB-C71A-4F2C-8594-C7BE339F9075}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0829FADB-C71A-4F2C-8594-C7BE339F9075}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0829FADB-C71A-4F2C-8594-C7BE339F9075}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0829FADB-C71A-4F2C-8594-C7BE339F9075}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{F46FF648-8B7A-4539-BFB4-42D99B902417} = {751B6494-C20C-41C9-9B84-2C73B49423F2}
		{3B43E09B-53C4-40E2-9D0B-4B13E2FB5BD2} = {751B6494-C20C-41C9-9B84-2C73B49423F2}
		{B9874010-B2F7-4D8E-A9E2-98560CB8640D} = {751B6494-C20C-41C9-9B84-2C73B49423F2}
		{33168D8D-8995-40D7-97B7-7C14FD66F0F9} = {751B6494-C20C-41C9-9B84-2C73B49423F2}
		{000F071F-7790-4A58-AED3-0152F31C3FF8} = {751B6494-C20C-41C9-9B84-2C73B49423F2}
		{0829FADB-C71A-4F2C-8594-C7BE339F9075} = {751B6494-C20C-41C9-9B84-2C73B49423F2}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {3F9504DF-AE36-4215-8133-01E16759A1FD}
	EndGlobalSection
EndGlobal
