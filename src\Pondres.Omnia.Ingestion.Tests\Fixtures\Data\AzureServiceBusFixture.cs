﻿using Microsoft.Azure.ServiceBus;
using Microsoft.Azure.ServiceBus.Core;
using Newtonsoft.Json;
using Pondres.Omnia.Ingestion.AzureServiceBus.Service;
using System.Text;

namespace Pondres.Omnia.Ingestion.Tests.Fixtures.Data;

public class AzureServiceBusFixture(ITestDependenciesProvider testDependenciesProvider, string serviceBusConnectionString)
{
    public static string TestCustomerQueueName => "ingestion-v2-testqueue";

    private readonly AzureServiceBusMessageReceiver messageReceiver = GetReceiver(testDependenciesProvider, serviceBusConnectionString);

    private readonly MessageSender sender = new(
        serviceBusConnection: new ServiceBusConnection(namespaceConnectionString: serviceBusConnectionString),
        entityPath: TestCustomerQueueName);

    public async Task<Message> PublishAsync<TMessage>(TMessage messageData, Dictionary<string, object> userProperties)
    {
        var message = new Message(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(messageData)))
        {
            MessageId = Guid.NewGuid().ToString()
        };

        foreach (var property in userProperties)
            message.UserProperties.Add(property);

        await sender.SendAsync(message);

        return message;
    }

    public async Task<Message> PublishAsync<TMessage>(TMessage messageData)
    {
        return await PublishAsync(messageData, new Dictionary<string, object>());
    }

    /// <summary>
    /// Cleans up the test customer ingestion queue. We can't make temporary queues per customer because of the azure subscription.
    /// If there are existing messages in the queue at the start of the test, the test will fail.
    /// Additionally, the tests in this project run sequentially.
    /// </summary>
    public async Task CleanTestQueueAsync()
    {
        IList<Message> messages;

        do
        {
            messages = await messageReceiver.ReceiveAsync(25) ?? [];

            await messageReceiver.CompleteAsync(messages);

        } while (messages.Count != 0);
    }

    /// <summary>
    /// Receives messages from the queue without completing them. Used for testing to verify message acknowledgment behavior.
    /// </summary>
    public async Task<IList<Message>> ReceiveMessagesAsync(int maxMessageCount)
    {
        return await messageReceiver.ReceiveAsync(maxMessageCount) ?? [];
    }

    /// <summary>
    /// Important to use the same receiver as the application because of the prefetch count.
    /// If we use another instance, the prefetched messages will not be cleaned up and tests fail.
    /// </summary>
    private static AzureServiceBusMessageReceiver GetReceiver(ITestDependenciesProvider testDependenciesProvider, string serviceBusConnectionString)
    {
        var provider = testDependenciesProvider.ResolveAzureServiceBusService<AzureServiceBusMessageReceiverProvider>();

        return provider.GetReceiver(serviceBusConnectionString, TestCustomerQueueName, 0);
    }
}
