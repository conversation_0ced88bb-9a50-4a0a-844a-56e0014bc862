﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <UserSecretsId>************************************</UserSecretsId>
    <DockerfileContext>..\..</DockerfileContext>
    <DockerComposeProjectPath>..\..\docker-compose.dcproj</DockerComposeProjectPath>
  </PropertyGroup>

  <ItemGroup>
    <Content Remove="appsettings.Development.json" />
  </ItemGroup>

  <ItemGroup>
    <None Include="appsettings.Development.json" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration.UserSecrets" Version="9.0.8" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.22.0" />
    <PackageReference Include="ShopifySharp" Version="6.24.1" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.58" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Common\Pondres.Omnia.Ingestion.Common\Pondres.Omnia.Ingestion.Common.csproj" />
  </ItemGroup>

</Project>
