﻿using FluentValidation;
using Pondres.Omnia.Ingestion.Common.Extensions;
using Pondres.Omnia.Ingestion.Common.Model;
using Pondres.Omnia.Ingestion.Common.Service;
using Serilog;
using Solude.Packages.Common;

namespace Pondres.Omnia.Ingestion.SendGrid.Commands;

public class IngestBulkOrderForTriggerFileCommandValidator : AbstractValidator<IngestBulkOrderForTriggerFileCommand>
{
    public IngestBulkOrderForTriggerFileCommandValidator()
    {
        RuleFor(x => x.FlowName).NotEmpty();
        RuleFor(x => x.RequestId).NotEmpty();
        RuleFor(x => x.Subject).NotEmpty();
        RuleFor(x => x.Customer).NotEmpty();
    }
}

public record IngestBulkOrderForTriggerFileCommand(
    string FlowName,
    string RequestId,
    string Subject,
    string Customer);

public class IngestBulkOrderForTriggerFileCommandHandler(
    IOrderService orderService,
    IEventService eventService,
    CustomerProvider customerProvider)
{
    public async Task<CommandResult> HandleAsync(IngestBulkOrderForTriggerFileCommand command)
    {
        try
        {
            var validationResult = await new IngestBulkOrderForTriggerFileCommandValidator().ValidateAsync(command);

            if (!validationResult.IsValid)
            {
                await eventService.PublishIngestionValidationFailedAsync(
                    result: validationResult,
                    requestId: command.RequestId,
                    flowName: command.FlowName,
                    customer: command.Customer);

                return CommandResult.ValidationError(validationResult);
            }

            await CreateTriggerFileOrderAsync(command);

            return CommandResult.Successful();
        }
        catch (Exception ex)
        {
            await eventService.PublishIngestionFailureAsync(ex, command.RequestId, command.FlowName, command.Customer);

            Log.Error(ex, "Failed to ingest trigger file order");

            return CommandResult.Error(ex.Message);
        }
    }

    private async Task CreateTriggerFileOrderAsync(IngestBulkOrderForTriggerFileCommand command)
    {
        var customerDto = await customerProvider.GetCustomerDetailsAsync(command.Customer);

        // Subject Example: "/blobServices/default/containers/<containerName>/blobs/{optionally-more-folders}/<triggerFileName>.finish"
        var subjectFragments = command.Subject.Split("/");
        var sourceStorageContainer = subjectFragments[4];
        var triggerFileName = subjectFragments[^1];
        var blobPrefix = string.Join(string.Empty, subjectFragments.Skip(6).ToList()[..^1]); // Skip everything until the blob path, remove the trigger file name from the end
        blobPrefix = string.IsNullOrWhiteSpace(blobPrefix) ? null : blobPrefix;

        var model = new OrderCreationModel(
            CustomerReference: Path.GetFileNameWithoutExtension(triggerFileName),
            OrderId: Guid.NewGuid(),
            FileName: triggerFileName,
            FlowName: command.FlowName,
            FlowId: string.Empty,
            Categories: new(),
            Customer: command.Customer,
            SourceName: "BulkSendGridIngestion",
            RequestId: command.RequestId,
            CustomerStorageAccountName: customerDto.Customer.StorageAccountName);

        await orderService.CreateOrderAsync(
            orderCreationModel: model,
            saveOrderDataFuncAsync: async blobStorage =>
            {
                await blobStorage.CopyAllFilesToContainerAsync(
                    sourceStorageContainer: sourceStorageContainer,
                    destinationStorageContainer: OrderService.OrderDataContainerName,
                    destinationFolderPath: model.ToRawDataFolderPath(),
                    sourcePrefix: blobPrefix,
                    filterFunc: blob => !blob.Name.EndsWith(triggerFileName));
            },
            cleanupFuncAsync: async blobStorage =>
            {
                await blobStorage.CopyAllFilesToContainerAsync(
                    sourceStorageContainer: sourceStorageContainer,
                    destinationStorageContainer: $"{sourceStorageContainer}-processed",
                    sourcePrefix: blobPrefix,
                    destinationFolderPath: $"{DateTimeOffset.Now:yyyyMMddTHHmmss}_{Path.GetFileNameWithoutExtension(triggerFileName)}");

                await blobStorage.DeleteAllFilesInContainerAsync(
                    containerName: sourceStorageContainer,
                    sourcePrefix: blobPrefix);
            });
    }
}
