﻿using MassTransit;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Pondres.Omnia.Ingestion.Common.Provider;
using Solude.CustomerProvider;
using Pondres.Omnia.Ingestion.Shopify;
using Pondres.Omnia.Ingestion.Shopify.Services;
using Pondres.Omnia.Ingestion.Tests.Fixtures.Data;

namespace Pondres.Omnia.Ingestion.Tests.Fixtures;

public class ShopifyAppFixture(
    ContainersFixture containers,
    string cosmosDbEndpoint) : BaseAppFixture<Program>(containers)
{
    private static readonly Mock<ICustomerRequestSender> customerRequestSender = new();

    private static readonly Mock<IHttpClientFactory> clientFactoryMock = new(MockBehavior.Strict);
    public ShopifyApiFixture ShopifyApiFixture { get; private set; } = new(clientFactoryMock);
    public CustomerRequestSenderFixture CustomerRequestSender { get; private set; } = new(customerRequestSender);

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureAppConfiguration((context, builder) =>
        {
            var configuration = new Dictionary<string, string?>
            {
                // Overwrite configuration with connectionstrings to the created services
                { "DataSeedDisabled", "true" },
                { "AzuriteConnectionString", Containers.Azurite.GetConnectionString() },
                { "RabbitConnectionString", Containers.RabbitMq.GetConnectionString() },
                { "RedisConnectionString", Containers.Redis.GetConnectionString() },
                { "CosmosDbEndpoint", cosmosDbEndpoint },
                { "IsDevelopment", "true" }
            };

            builder.AddInMemoryCollection(configuration);
        });
        builder.ConfigureTestServices(services =>
        {
            services.Remove(services.Single(x => x.ServiceType == typeof(IHttpClientFactory)));
            services.AddSingleton(clientFactoryMock.Object);

            services.Remove(services.Single(x => x.ServiceType == typeof(ISecretProvider)));
            services.AddSingleton<ISecretProvider>(new EnvironmentSecretProvider());

            services.Remove(services.Single(x => x.ServiceType == typeof(ICustomerRequestSender)));
            services.AddSingleton(customerRequestSender.Object);

            services.RemoveHostedService<ShopifyOrderFetchBackgroundService>();
            services.AddSingleton<ShopifyOrderFetchBackgroundService>();
        });

        builder.UseEnvironment("Development");
    }
}
