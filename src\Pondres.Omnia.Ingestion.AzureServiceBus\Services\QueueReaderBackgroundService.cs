﻿using Pondres.Omnia.Ingestion.AzureServiceBus.Commands;
using Pondres.Omnia.Ingestion.AzureServiceBus.Entities;
using Pondres.Omnia.Ingestion.AzureServiceBus.Queries;
using Serilog;

namespace Pondres.Omnia.Ingestion.AzureServiceBus.Services;

public class QueueReaderBackgroundService(
    QueueQueries queries,
    ReadAzureServiceBusQueueCommandHandler queueCommandHandler) : BackgroundService
{
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        // Not convenient to test this because of existing configs in cosmos from previous runs 

        using var timer = new PeriodicTimer(TimeSpan.FromSeconds(15));

        do
        {
            Log.Information("Reading service bus queues");

            var queueRegistrations = await queries.GetActiveQueuesAsync();

            Log.Information("Found {QueueCount} service bus queues to read", queueRegistrations.Count);

            foreach (var registration in queueRegistrations)
            {
                foreach (var queue in registration.Queues)
                {
                    await ReadQueueAsync(registration, queue);
                }
            }
        }  while (!stoppingToken.IsCancellationRequested && await timer.WaitForNextTickAsync(stoppingToken));
    }

    public async Task ReadQueueAsync( // Exposed for testing. If it's on a consumer, it can be made private.
        AzureServiceBusQueueRegistrationsEntity registration,
        AzureServiceBusCustomerQueueRegistrationEntity queue)
    {
        Log.Information("Reading service bus queue {QueueName}", queue.QueueName);

        var command = CreateReadCommand(registration, queue);

        var result = await queueCommandHandler.HandleAsync(command);

        if (result.IsFailed)
            Log.Error("Error while reading queue: {Reasons}", result.Reasons);
        else
            Log.Information("Queue {QueueName} read successfully", queue.QueueName);
    }

    private static ReadAzureServiceBusQueueCommand CreateReadCommand(AzureServiceBusQueueRegistrationsEntity registration, AzureServiceBusCustomerQueueRegistrationEntity queue) =>
        new(QueueName: queue.QueueName,
            Customer: registration.Customer,
            CustomerVaultName: registration.VaultName,
            CustomerStorageAccountName: registration.StorageAccountName,
            FlowName: queue.FlowName,
            ConnectionStringSecretName: queue.ConnectionStringSecretName,
            CustomerReferenceDeterminationValue: queue.CustomerReferenceDeterminationValue,
            CustomerReferenceDeterminationType: queue.CustomerReferenceDeterminationType);
}
