﻿using Pondres.Omnia.Ingestion.Common.Extensions;
using Pondres.Omnia.Ingestion.Shopify.Config;
using Pondres.Omnia.Ingestion.Shopify.Extensions;
using Pondres.Omnia.Ingestion.Shopify.Queries;
using Pondres.Omnia.Ingestion.Shopify.Services;
using ShopifySharp;
using Solude.ApiBase.Extensions;

namespace Pondres.Omnia.Ingestion.Shopify;

public class Startup(IHostEnvironment environment, IConfiguration configuration)
{
    private readonly ShopifyAppSettings appSettings = configuration.Get<ShopifyAppSettings>() ??
        throw new InvalidOperationException("AppSettings not bindable");

    public static void Configure(IApplicationBuilder app)
    {
        app.UseDefaultServiceDependencies();
    }

    public void ConfigureServices(IServiceCollection services)
    {
        services.AddDefaultServiceDependencies(appSettings, Program.Name);

        services.AddCommonDependencies(environment, appSettings);

        services.AddHostedService<ShopifyOrderFetchBackgroundService>();

        services.AddTransient<ShopifyIntegrationService>();

        services.AddTransient<ShopifyEnvironmentQueries>();

        services.AddRedis(appSettings.RedisConnectionString);

        services.AddHttpClient();
    }
}