﻿using Pondres.Omnia.Ingestion.Shopify.Queries;
using Serilog;

namespace Pondres.Omnia.Ingestion.Shopify.Services;

public class ShopifyOrderFetchBackgroundService(
    ShopifyIntegrationService shopifyService,
    ShopifyEnvironmentQueries queries) : BackgroundService
{
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        using var timer = new PeriodicTimer(TimeSpan.FromMinutes(5));

        do
        {
            await FetchAndImportOrdersAsync();

        } while (!stoppingToken.IsCancellationRequested && await timer.WaitForNextTickAsync(stoppingToken));
    }

    public async Task FetchAndImportOrdersAsync(string? customer = null)
    {
        try
        {
            var environments = await queries.GetActiveEnvironmentsAsync();

            foreach (var environment in environments.Where(x => customer == null || x.Customer == customer))
                await shopifyService.ImportAllAssignedFulfillmentOrdersAsync(environment);
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Failed to import shopify orders.");
        }
    }
}

