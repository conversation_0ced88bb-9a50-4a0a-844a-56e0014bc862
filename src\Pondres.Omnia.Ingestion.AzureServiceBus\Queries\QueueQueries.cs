﻿using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Cosmos.Linq;
using Pondres.Omnia.Ingestion.AzureServiceBus.Entities;
using Pondres.Omnia.Ingestion.Common.Config;

namespace Pondres.Omnia.Ingestion.AzureServiceBus.Queries;

public class QueueQueries(CosmosClient client)
{
    public async Task<List<AzureServiceBusQueueRegistrationsEntity>> GetActiveQueuesAsync()
    {
        var configurationContainer = client
            .GetDatabase(CosmosConfiguration.IngestionDatabaseName)
            .GetContainer(CosmosConfiguration.ConfigurationContainerName);

        var iterator = configurationContainer.GetItemLinqQueryable<AzureServiceBusQueueRegistrationsEntity>()
            .Where(x => x.Type == CosmosConfiguration.ConfigurationAzureServiceBusQueueTypeName && x.Active)
            .ToFeedIterator();

        var entities = new List<AzureServiceBusQueueRegistrationsEntity>();

        while (iterator.HasMoreResults)
        {
            var response = await iterator.ReadNextAsync();
            entities.AddRange(response);
        }

        return entities;
    }
}
