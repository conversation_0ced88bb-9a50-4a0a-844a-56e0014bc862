﻿using FluentAssertions;
using Microsoft.Azure.ServiceBus;
using Pondres.Omnia.Ingestion.AzureServiceBus.Entities;
using Pondres.Omnia.Ingestion.AzureServiceBus.Services;
using Pondres.Omnia.Ingestion.Common.Model;
using Pondres.Omnia.Ingestion.Tests.Fixtures;
using Pondres.Omnia.Ingestion.Tests.Helper;
using Pondres.Omnia.Ingestion.V2.Contracts.Events;
using Pondres.Omnia.OrderHub.Contracts.Order;
using System.Text;
using Xunit;

namespace Pondres.Omnia.Ingestion.Tests.IntegrationTests.Ingestion;

[Collection("IntegrationTests")]
public class AzureServiceBusIngestionTests(IntegrationTestFixture fixture) : BaseIntegrationTest(fixture)
{
    [Fact]
    public async Task PerformBackgroundCheck_ValidMessage_CreatesOrder()
    {
        // Arrange
        var customer = GetCustomer();
        var configuration = await fixture.Configuration.CreateAzureServiceBusConfigurationAsync(customer);
        var orderData = new SampleOrderData("1234");

        // Act
        var message = await SendMessageAndReadFromQueue(configuration, orderData);

        // Assert
        fixture.AzureServiceBusApp.BusPublisherObserver.Should().NotHaveSeenMessage<IngestionFailure>(x => x.Customer == customer);

        var orderCreated = fixture.AzureServiceBusApp.BusPublisherObserver.Should().HaveSeenMessage<CreateOrderMessage>(x => x.Customer == customer);
        orderCreated.Source.Should().Be("AzureServiceBus");
        orderCreated.Customer.Should().Be(customer);
        orderCreated.OrderId.Should().NotBe(Guid.Empty);
        orderCreated.RequestId.Should().Be(message.MessageId);
        orderCreated.CustomerReference.Should().Be(orderData.CustomerReference);
        orderCreated.Flow.Should().Be(configuration.Queues[0].FlowName);
        orderCreated.Categories.Should().NotBeNull();
        orderCreated.DataContainerName.Should().Be("orderdata");

        var file = await fixture.Blob.DownloadOrderDataAsync<SampleOrderData>(orderCreated);
        file.CustomerReference.Should().Be(orderData.CustomerReference);
    }

    [Fact]
    public async Task PerformBackgroundCheck_FlowFromMessageProperties_CreatesOrder()
    {
        // Arrange
        var customer = GetCustomer();
        var configuration = await fixture.Configuration.CreateAzureServiceBusConfigurationAsync(
            customer: customer,
            flowName: string.Empty);

        var orderData = new SampleOrderData("1234");

        var userProperties = new Dictionary<string, object>
        {
            { "x-flow", "SomeFlow" }
        };

        // Act
        var message = await SendMessageAndReadFromQueue(
            configuration: configuration,
            orderData: orderData,
            userProperties: userProperties);

        // Assert
        fixture.AzureServiceBusApp.BusPublisherObserver.Should().NotHaveSeenMessage<IngestionFailure>(x => x.Customer == customer);

        var orderCreated = fixture.AzureServiceBusApp.BusPublisherObserver.Should().HaveSeenMessage<CreateOrderMessage>(x => x.Customer == customer);
        orderCreated.Source.Should().Be("AzureServiceBus");
        orderCreated.Customer.Should().Be(customer);
        orderCreated.OrderId.Should().NotBe(Guid.Empty);
        orderCreated.RequestId.Should().Be(message.MessageId);
        orderCreated.CustomerReference.Should().Be(orderData.CustomerReference);
        orderCreated.Flow.Should().Be("SomeFlow");
        orderCreated.Categories.Should().NotBeNull();
        orderCreated.DataContainerName.Should().Be("orderdata");

        var file = await fixture.Blob.DownloadOrderDataAsync<SampleOrderData>(orderCreated);
        file.CustomerReference.Should().Be(orderData.CustomerReference);
    }

    [Fact]
    public async Task PerformBackgroundCheck_CustomerReferenceFromMessageProperties_CreatesOrder()
    {
        // Arrange
        var customer = GetCustomer();
        var configuration = await fixture.Configuration.CreateAzureServiceBusConfigurationAsync(
            customer: customer,
            determinationValue: "x-customer-reference",
            determinationType: CustomerReferenceDeterminationType.UserMessageProperty);

        var orderData = new SampleOrderData(string.Empty);

        var userProperties = new Dictionary<string, object>
        {
            { "x-customer-reference", "SomeReference" }
        };

        // Act
        var message = await SendMessageAndReadFromQueue(
            configuration: configuration,
            orderData: orderData,
            userProperties: userProperties);

        // Assert
        fixture.AzureServiceBusApp.BusPublisherObserver.Should().NotHaveSeenMessage<IngestionFailure>(x => x.Customer == customer);

        var orderCreated = fixture.AzureServiceBusApp.BusPublisherObserver.Should().HaveSeenMessage<CreateOrderMessage>(x => x.Customer == customer);
        orderCreated.Source.Should().Be("AzureServiceBus");
        orderCreated.Customer.Should().Be(customer);
        orderCreated.OrderId.Should().NotBe(Guid.Empty);
        orderCreated.RequestId.Should().Be(message.MessageId);
        orderCreated.CustomerReference.Should().Be("SomeReference");
        orderCreated.Flow.Should().Be("AzureServiceBus");
        orderCreated.Categories.Should().NotBeNull();
        orderCreated.DataContainerName.Should().Be("orderdata");

        var file = await fixture.Blob.DownloadOrderDataAsync<SampleOrderData>(orderCreated);
        file.CustomerReference.Should().Be(orderData.CustomerReference);
    }

    [Fact]
    public async Task PerformBackgroundCheck_MissingCustomerReference_IngestionFailed()
    {
        // Arrange
        var customer = GetCustomer();
        var configuration = await fixture.Configuration.CreateAzureServiceBusConfigurationAsync(customer);

        // Act
        var message = await SendMessageAndReadFromQueue(configuration, new { });

        // Assert
        var ingestionFailed = fixture.AzureServiceBusApp.BusPublisherObserver.Should().HaveSeenMessage<IngestionFailure>(x => x.Customer == customer);

        ingestionFailed.Message.Should().Be("Customer reference could not be determined");
        ingestionFailed.ExceptionData.Should().Contain(configuration.Queues[0].CustomerReferenceDeterminationValue);
        ingestionFailed.ExceptionData.Should().Contain(Encoding.UTF8.GetString(message.Body));
        ingestionFailed.RequestId.Should().NotBeEmpty();
        ingestionFailed.Flow.Should().Be(configuration.Queues[0].FlowName);
        ingestionFailed.Customer.Should().Be(customer);
    }

    [Fact]
    public async Task PerformBackgroundCheck_UnsuportedDeterminationType_NothingHappens()
    {
        // Arrange
        var customer = GetCustomer();
        var configuration = await fixture.Configuration.CreateAzureServiceBusConfigurationAsync(customer, determinationType: CustomerReferenceDeterminationType.None);

        // Act
        var message = await SendMessageAndReadFromQueue(configuration, new { });

        // Assert
        fixture.AzureServiceBusApp.BusPublisherObserver.Should().NotHaveSeenMessage<IngestionFailure>(x => x.Customer == customer);
        fixture.AzureServiceBusApp.BusPublisherObserver.Should().NotHaveSeenMessage<CreateOrderMessage>(x => x.Customer == customer);
    }

    [Fact]
    public async Task PerformBackgroundCheck_NoMessages_NothingHappens()
    {
        // Arrange
        var customer = GetCustomer();
        var configuration = await fixture.Configuration.CreateAzureServiceBusConfigurationAsync(customer, determinationType: CustomerReferenceDeterminationType.None);
        var service = fixture.ResolveAzureServiceBusService<QueueReaderBackgroundService>();

        // Act
        await service.ReadQueueAsync(configuration, configuration.Queues[0]);

        // Assert
        fixture.AzureServiceBusApp.BusPublisherObserver.Should().NotHaveSeenMessage<IngestionFailure>(x => x.Customer == customer);
        fixture.AzureServiceBusApp.BusPublisherObserver.Should().NotHaveSeenMessage<CreateOrderMessage>(x => x.Customer == customer);
    }

    [Fact]
    public async Task PerformBackgroundCheck_MixedValidAndInvalidMessages_ProcessesIndividually()
    {
        // Arrange
        var customer = GetCustomer();
        var configuration = await fixture.Configuration.CreateAzureServiceBusConfigurationAsync(customer);

        await fixture.ServiceBus.CleanTestQueueAsync();

        // Send a valid message
        var validOrderData = new SampleOrderData("ValidCustomerRef");
        var validMessage = await fixture.ServiceBus.PublishAsync(validOrderData);

        // Send an invalid message (missing customer reference)
        var invalidMessage = await fixture.ServiceBus.PublishAsync(new { });

        // Send another valid message
        var validOrderData2 = new SampleOrderData("ValidCustomerRef2");
        var validMessage2 = await fixture.ServiceBus.PublishAsync(validOrderData2);

        var service = fixture.ResolveAzureServiceBusService<QueueReaderBackgroundService>();

        // Act
        await TestHelper.WaitForAsync(
            wait: TimeSpan.FromSeconds(5), // Waiting a bit because it's possible for the message to not be there yet
            forFunc: async () =>
            {
                await service.ReadQueueAsync(configuration, configuration.Queues[0]);

                // Assert
                // Should have one ingestion failure for the invalid message
                var ingestionFailed = fixture.AzureServiceBusApp.BusPublisherObserver.Should().HaveSeenMessage<IngestionFailure>(x => x.Customer == customer);
                ingestionFailed.Message.Should().Be("Customer reference could not be determined");
            });

        // Should have two successful orders created for the valid messages
        var orderCreatedMessages = fixture.AzureServiceBusApp.BusPublisherObserver.Should().HaveSeenMessages<CreateOrderMessage>(x => x.Customer == customer, 2);
        orderCreatedMessages.Should().HaveCount(2);

        var firstOrder = orderCreatedMessages.First(x => x.RequestId == validMessage.MessageId);
        firstOrder.CustomerReference.Should().Be("ValidCustomerRef");

        var secondOrder = orderCreatedMessages.First(x => x.RequestId == validMessage2.MessageId);
        secondOrder.CustomerReference.Should().Be("ValidCustomerRef2");

        // Verify no messages remain in the queue (all were acknowledged individually)
        var remainingMessages = await fixture.ServiceBus.ReceiveMessagesAsync(10);
        remainingMessages.Should().BeEmpty("All messages should have been acknowledged individually");
    }

    private async Task<Message> SendMessageAndReadFromQueue<TData>(AzureServiceBusQueueRegistrationsEntity configuration, TData orderData, Dictionary<string, object>? userProperties = null)
    {
        await fixture.ServiceBus.CleanTestQueueAsync();

        var message = await fixture.ServiceBus.PublishAsync(orderData, userProperties ?? []);

        var service = fixture.ResolveAzureServiceBusService<QueueReaderBackgroundService>();

        await service.ReadQueueAsync(configuration, configuration.Queues[0]);

        return message;
    }
}

public record SampleOrderData(string CustomerReference);