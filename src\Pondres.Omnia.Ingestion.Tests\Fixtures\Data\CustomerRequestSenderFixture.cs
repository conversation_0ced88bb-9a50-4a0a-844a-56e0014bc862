﻿using Moq;
using Pondres.Omnia.Customer.Contracts.Request;
using Pondres.Omnia.Customer.Contracts.Shared;
using Solude.CustomerProvider;

namespace Pondres.Omnia.Ingestion.Tests.Fixtures.Data;
public class CustomerRequestSenderFixture(Mock<ICustomerRequestSender> mock)
{
    public GetCustomerResponse SetupCustomerResponse(GetCustomerResponse response)
    {
        mock.Setup(x => x.SendBusRequestAsync<GetCustomerRequest, GetCustomerResponse>(It.Is<GetCustomerRequest>(x => x.Id == response.Customer.Id)))
            .ReturnsAsync(response);

        return response;
    }

    public GetCustomerResponse SetupSuccessCustomerResponse(string customer) => SetupCustomerResponse(
        new GetCustomerResponse()
        {
            Customer = new Customer.Contracts.Shared.Customer()
            {
                Id = customer,
                Name = $"{customer}_Name",
                VaultName = "vault",
                StorageAccountName = "devstoreaccount1",
                Features = [FeatureTypes.AuditLogging.ToString()]
            },
            Error = string.Empty,
            Success = true
        });
}
