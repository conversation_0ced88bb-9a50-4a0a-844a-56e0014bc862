﻿using Microsoft.AspNetCore.Mvc;
using Pondres.Omnia.Ingestion.Common.Handlers;
using Pondres.Omnia.Ingestion.Common.Model;
using Solude.ApiBase.Contracts.Api;
using Solude.ApiBase.Extensions;

namespace Pondres.Omnia.Ingestion.Api.Internal.Controllers;

[Route("submit")]
public class InternalIngestionController(CreateOrderCommandHandler orderCommandHandler) : ControllerBase
{
    [HttpPost]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResultDto))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResultDto))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResultDto))]
    [ProducesResponseType(StatusCodes.Status409Conflict, Type = typeof(ApiResultDto))]
    public async Task<IActionResult> IngestInternalOrderAsync(
        IFormFile content,
        [FromHeader(Name = "x-customer-reference")] string customerReference,
        [FromHeader(Name = "x-flow")] string flowName,
        [FromHeader(Name = "x-flow-id")] string? flowId,
        [FromHeader(Name = "x-customer")] string customer,
        [FromHeader(Name = "x-category-one")] string categoryOne,
        [FromHeader(Name = "x-category-two")] string categoryTwo,
        [FromHeader(Name = "x-category-three")] string categoryThree)
    {
        using var stream = new MemoryStream();

        await content.CopyToAsync(stream);

        var command = new CreateOrderCommand(
            Customer: customer,
            CustomerReference: customerReference,
            FlowName: flowName,
            FlowId: flowId,
            Source: "InternalApiIngestion",
            Categories: new OrderCreationModelCategories()
            {
                One = categoryOne,
                Two = categoryTwo,
                Three = categoryThree
            });

        var result = await orderCommandHandler.HandleAsync(command, stream);

        return result.ToActionResult();
    }
}
