﻿using System;

namespace Pondres.Omnia.Ingestion.V2.Contracts.Events
{
    public class IngestionDataReceived
    {
        public string Customer { get; set; }

        public string CustomerReference { get; set; }
        public string Flow { get; set; }

        public string RequestId { get; set; }

        public string Source { get; set; }
        public DateTimeOffset Timestamp { get; set; }
    }
}