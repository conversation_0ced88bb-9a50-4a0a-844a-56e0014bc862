﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<UserSecretsId>48e3e024-af5b-4ef3-aa93-17d2ff4a6fde</UserSecretsId>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<DockerfileContext>.</DockerfileContext>
		<DockerComposeProjectPath>..\..\docker-compose.dcproj</DockerComposeProjectPath>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.22.0" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.3" />
		<PackageReference Include="Pondres.Omnia.Customer.Contracts" Version="1.20250804.1" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Common\Pondres.Omnia.Ingestion.Common\Pondres.Omnia.Ingestion.Common.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Content Update="appsettings.Development.json">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	    <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
	    <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
	  </Content>
	  <Content Update="appsettings.json">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	    <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
	    <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
	  </Content>
	  <Content Update="appsettings.Production.json">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	    <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
	    <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
	  </Content>
	  <Content Update="appsettings.Staging.json">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	    <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
	    <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
	  </Content>
	  <Content Update="appsettings.Test.json">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	    <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
	    <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
	  </Content>
	</ItemGroup>
	
</Project>
