﻿using Azure;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Polly;
using Polly.Retry;
using Pondres.Omnia.Ingestion.Common.Exceptions;
using Serilog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Pondres.Omnia.Ingestion.Common.Storage;

public class BlobStorageRepository(
    BlobServiceClient customerStorageAccountClient,
    Func<Uri, BlobClient> getBlobClientFunc) : IBlobStorageRepository
{
    private readonly BlobServiceClient customerStorageAccountClient = customerStorageAccountClient;

    private readonly Func<Uri, BlobClient> getBlobClientFunc = getBlobClientFunc;

    private readonly AsyncRetryPolicy asyncRetryPolicy = Policy.Handle<TaskCanceledException>().RetryAsync(3);

    public async Task CreateContainerIfNotExistsAsync(string destinationStorageContainer) =>
        await customerStorageAccountClient.GetBlobContainerClient(destinationStorageContainer).CreateIfNotExistsAsync();

    public async Task CopyAllFilesToContainerAsync(
       string sourceStorageContainer,
       string destinationStorageContainer,
       string destinationFolderPath,
       string? sourcePrefix = null,
       Func<BlobItem, bool>? filterFunc = null)
    {
        try
        {
            var sourceContainer = customerStorageAccountClient.GetBlobContainerClient(sourceStorageContainer);
            var destinationContainer = customerStorageAccountClient.GetBlobContainerClient(destinationStorageContainer);

            await destinationContainer.CreateIfNotExistsAsync();

            // Process blobs in pages of 100 to handle large numbers of blobs efficiently
            await foreach (var page in sourceContainer.GetBlobsAsync(prefix: sourcePrefix).AsPages(pageSizeHint: 100))
            {
                var copyOperations = new List<CopyFromUriOperation>();

                foreach (var blobItem in page.Values)
                {
                    if (filterFunc != null && !filterFunc(blobItem))
                        continue;

                    var sourceBlobClient = sourceContainer.GetBlobClient(blobItem.Name);

                    var fileName = !string.IsNullOrWhiteSpace(sourcePrefix) && blobItem.Name.StartsWith(sourcePrefix) ?
                        blobItem.Name[sourcePrefix.Length..].TrimStart('/') :
                        blobItem.Name;

                    var destinationBlobClient = destinationContainer.GetBlobClient($"{destinationFolderPath}/{fileName}");

                    var copyOperation = await destinationBlobClient.StartCopyFromUriAsync(sourceBlobClient.Uri);

                    copyOperations.Add(copyOperation);
                }

                var failedResponses = new List<Response>();

                foreach (var operation in copyOperations)
                {
                    var response = await operation.WaitForCompletionResponseAsync();

                    if (response.IsError)
                        failedResponses.Add(response);
                }

                if (failedResponses.Count > 0)
                    throw new BlobSaveException($"Failed to copy {failedResponses.Count} blob(s): {string.Join(", ", failedResponses.Select(x => x.ReasonPhrase))}");
            }

            Log.Information("Completed blob copy");
        }
        catch (Exception exc)
        {
            throw new BlobSaveException("Exception during blob save", exc);
        }
    }

    public async Task CopySingleFileToContainerAsync(
        Uri sourceUri,
        string destinationStorageContainer,
        string destinationPath)
    {
        try
        {
            Log.Information("Starting blob copy");

            var sourceBlobClient = getBlobClientFunc(sourceUri);

            var destinationContainer = customerStorageAccountClient.GetBlobContainerClient(destinationStorageContainer);
            var targetBlobClient = destinationContainer.GetBlobClient(destinationPath);

            if (customerStorageAccountClient.AccountName != "devstoreaccount1")
            {
                Log.Information("Executing sync copy from {SourceUri} to destination {Destination}",
                    sourceUri, targetBlobClient.Uri);

                var sourceBlobSasUri = await AzureSharedAccessUrlGenerator.GetSharedAccessSignatureUrlForBlobAsync(
                    client: customerStorageAccountClient,
                    blobClient: sourceBlobClient,
                    expireTime: TimeSpan.FromMinutes(10));

                await targetBlobClient.SyncCopyFromUriAsync(sourceBlobSasUri);
            }
            else
            {
                Log.Information("Downloading from {SourceUri} and uploading to {Destination}",
                    sourceUri, targetBlobClient.Uri);

                var blobContent = await sourceBlobClient.DownloadAsync();
                await targetBlobClient.UploadAsync(blobContent.Value.Content);
            }

            Log.Information("Completed blob copy");
        }
        catch (Exception exc)
        {
            throw new BlobSaveException("Exception during blob save", exc);
        }
    }

    public async Task CreateAndSaveToContainerAsync(
        Stream stream,
        string destinationStorageContainer,
        string destinationPath)
    {
        try
        {
            var containerClient = customerStorageAccountClient.GetBlobContainerClient(destinationStorageContainer);

            Log.Information("Saving blob to path '{BlobPath}' in '{StorageContainer}'", destinationPath, destinationStorageContainer);

            await asyncRetryPolicy.ExecuteAsync(async () =>
            {
                await containerClient.UploadBlobAsync(destinationPath, stream);
            });
        }
        catch (Exception exc)
        {
            throw new BlobSaveException("Exception during blob save", exc);
        }
    }

    public async Task DeleteFileAsync(Uri fileUri)
    {
        Log.Information("Deleting blob {FileUri}", fileUri);

        await getBlobClientFunc(fileUri).DeleteAsync();
    }

    public async Task DeleteAllFilesInContainerAsync(string containerName, string? sourcePrefix)
    {
        Log.Information("Deleting blobs {FileUri}", containerName);

        var containerClient = customerStorageAccountClient.GetBlobContainerClient(containerName);

        // Process blobs in pages of 100 to handle large numbers of blobs efficiently
        await foreach (var page in containerClient.GetBlobsAsync(prefix: sourcePrefix).AsPages(pageSizeHint: 100))
        {
            foreach (var blobItem in page.Values)
            {
                var blobClient = containerClient.GetBlobClient(blobItem.Name);

                await blobClient.DeleteIfExistsAsync(DeleteSnapshotsOption.IncludeSnapshots);
            }
        }
    }
}