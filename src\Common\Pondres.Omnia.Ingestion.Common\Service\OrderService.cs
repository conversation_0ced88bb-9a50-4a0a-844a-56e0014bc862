﻿using MassTransit;
using Pondres.Omnia.Ingestion.Common.Exceptions;
using Pondres.Omnia.Ingestion.Common.Extensions;
using Pondres.Omnia.Ingestion.Common.Model;
using Pondres.Omnia.Ingestion.Common.Storage;
using Serilog;
using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace Pondres.Omnia.Ingestion.Common.Service;

public class OrderService(
    IBus bus,
    IBlobStorageRepositoryFactory blobStorageRepositoryFactory) : IOrderService
{
    public const string OrderDataContainerName = "orderdata";

    public async Task CreateSingleRawDataFileOrderAsync(
        Stream sourceStream,
        OrderCreationModel orderCreationModel)
    {
        await CreateOrderAsync(
            orderCreationModel: orderCreationModel,
            saveOrderDataFuncAsync: async (storageRepository) =>
            {
                var rawDataFilePath = $"{orderCreationModel.ToRawDataFolderPath()}/{orderCreationModel.FileName}";

                await storageRepository.CreateAndSaveToContainerAsync(
                    stream: sourceStream,
                    destinationStorageContainer: OrderDataContainerName,
                    destinationPath: rawDataFilePath);
            });
    }

    public async Task CreateOrderAsync(
        OrderCreationModel orderCreationModel,
        Func<IBlobStorageRepository, Task> saveOrderDataFuncAsync,
        Func<IBlobStorageRepository, Task>? cleanupFuncAsync = null)
    {
        try
        {
            await bus.Publish(orderCreationModel.ToDataReceivedEvent());

            var storageRepository = await blobStorageRepositoryFactory.GetStorageRepositoryAsync(orderCreationModel.CustomerStorageAccountName);

            await saveOrderDataFuncAsync(storageRepository);

            await bus.Publish(orderCreationModel.ToDataSavedEvent(OrderDataContainerName));

            await PublishCreateOrderMessageAsync(orderCreationModel);

            if (cleanupFuncAsync != null)
                await cleanupFuncAsync(storageRepository);
        }
        catch (Exception exc)
        {
            Log.Error(exc, "Exception during data save");

            await bus.Publish(orderCreationModel.ToDataSaveFailedEvent(exc, OrderDataContainerName));

            throw;
        }
    }

    private async Task PublishCreateOrderMessageAsync(
        OrderCreationModel orderCreationModel,
        CancellationToken cancellationToken = default)
    {
        try
        {
            Log.Information("Submitting order to order service");

            await bus.Publish(orderCreationModel.ToCreateOrderMessage(OrderDataContainerName), cancellationToken);
        }
        catch (Exception exception)
        {
            Log.Error("Error submitting order, creation model {@CreationModel}", orderCreationModel);

            throw new SubmitOrderException($"Failed to submit order for flow {orderCreationModel.FlowName}", exception);
        }
    }
}