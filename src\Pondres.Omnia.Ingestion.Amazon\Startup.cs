﻿using Pondres.Omnia.Ingestion.Amazon.Commands;
using Pondres.Omnia.Ingestion.Amazon.Config;
using Pondres.Omnia.Ingestion.Amazon.Queues;
using Pondres.Omnia.Ingestion.Amazon.Seed;
using Pondres.Omnia.Ingestion.Amazon.Services;
using Pondres.Omnia.Ingestion.Common.Extensions;
using Solude.ApiBase.Extensions;

namespace Pondres.Omnia.Ingestion.Amazon;

public class Startup(IHostEnvironment environment, IConfiguration configuration)
{
    private readonly AmazonAppSettings appSettings = configuration.Get<AmazonAppSettings>() ??
        throw new InvalidOperationException("AppSettings not bindable");

    public void Configure(IApplicationBuilder app)
    {
        app.UseDefaultServiceDependencies();
    }

    public void ConfigureServices(IServiceCollection services)
    {
        services.AddDefaultServiceDependencies(appSettings, Program.Name);
        services.AddCommonDependencies(environment, appSettings);
        services.AddDataSeed<DataSeed>();

        services.AddHostedService<QueueReaderBackgroundService>();

        services.AddTransient<QueueQueries>();
        services.AddTransient<ReadAwsQueueCommandHandler>();

        services.Configure<DataSeedSettings>(x =>
        {
            x.KeyVaultName = appSettings.TestCustomerKeyVaultName;
            x.StorageAccountName = appSettings.TestCustomerStorageAccountName;
            x.CustomerQueueUrl = appSettings.TestCustomerQueueUrl;
        });
    }
}