﻿using Newtonsoft.Json;

namespace Pondres.Omnia.Ingestion.SendGrid.Models;

public class SendGridBlobCreatedEvent
{
    [JsonProperty("topic")]
    public string Topic { get; set; } = string.Empty;

    [JsonProperty("subject")]
    public string Subject { get; set; } = string.Empty;

    [JsonProperty("eventType")]
    public string EventType { get; set; } = string.Empty;

    [JsonProperty("id")]
    public string Id { get; set; } = string.Empty;

    [JsonProperty("data")]
    public BlobData Data { get; set; } = new();

    [JsonProperty("dataVersion", NullValueHandling = NullValueHandling.Ignore)]
    public string? DataVersion { get; set; }

    [JsonProperty("metadataVersion", NullValueHandling = NullValueHandling.Ignore)]
    public string? MetadataVersion { get; set; }

    [JsonProperty("eventTime")]
    public DateTimeOffset EventTime { get; set; }
}
