﻿using FluentAssertions;
using Pondres.Omnia.Ingestion.Tests.Fixtures;
using Xunit;

namespace Pondres.Omnia.Ingestion.Tests.IntegrationTests;

[Collection("IntegrationTests")]
public class StartupTest(IntegrationTestFixture fixture) : BaseIntegrationTest(fixture)
{
    [Fact]
    public async Task StartupTestAsync()
    {
        // Arrange
        var azureServiceBusAppClient = fixture.AzureServiceBusApp.CreateClient();
        var internalIngestionAppClient = fixture.InternalApiApp.CreateClient();
        var externalIngestionAppClient = fixture.ExternalApiApp.CreateClient();
        var sendGridAppClient = fixture.SendGridApp.CreateClient();

        // Act
        var azureServiceBusAppResponse = await azureServiceBusAppClient.GetAsync("/health/ready");
        var internalIngestionAppResponse = await internalIngestionAppClient.GetAsync("/health/ready");
        var externalIngestionAppResponse = await externalIngestionAppClient.GetAsync("/health/ready");
        var sendGridAppClientAppResponse = await sendGridAppClient.GetAsync("/health/ready");

        // Assert
        azureServiceBusAppResponse.Should().BeSuccessful();
        internalIngestionAppResponse.Should().BeSuccessful();
        externalIngestionAppResponse.Should().BeSuccessful();
        sendGridAppClientAppResponse.Should().BeSuccessful();
    }
}
