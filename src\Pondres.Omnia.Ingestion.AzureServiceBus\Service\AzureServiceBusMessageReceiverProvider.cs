﻿using Microsoft.Azure.ServiceBus;
using Microsoft.Azure.ServiceBus.Core;
using System.Collections.Concurrent;

namespace Pondres.Omnia.Ingestion.AzureServiceBus.Service;

public class AzureServiceBusMessageReceiverProvider
{
    private readonly ConcurrentDictionary<string, AzureServiceBusMessageReceiver> receivers = new();

    public AzureServiceBusMessageReceiver GetReceiver(string connectionString, string queue, int prefetch)
    {
        var key = connectionString + queue;

        if (receivers.TryGetValue(key, out var cachedReceiver))
            return cachedReceiver;
        else
        {
            var receiver = GetMessageReceiver(connectionString, queue, prefetch);

            receivers.GetOrAdd(key, receiver);

            return receiver;
        }
    }

    private static AzureServiceBusMessageReceiver GetMessageReceiver(string endpointOrConnectionString, string queueName, int prefetchCount) =>
        new(messageReceiver: new MessageReceiver(
            new ServiceBusConnection(namespaceConnectionString: endpointOrConnectionString)
            {
                OperationTimeout = TimeSpan.FromMinutes(5)
            },
            queueName,
            ReceiveMode.PeekLock,
            retryPolicy: RetryPolicy.Default,
            prefetchCount: prefetchCount));
}
