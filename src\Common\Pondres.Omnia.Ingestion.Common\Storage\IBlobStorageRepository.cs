﻿using Azure.Storage.Blobs.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace Pondres.Omnia.Ingestion.Common.Storage;

public interface IBlobStorageRepository
{
    Task DeleteFileAsync(Uri fileUri);
    Task CopySingleFileToContainerAsync(Uri sourceUri, string destinationStorageContainer, string destinationPath);
    Task CreateAndSaveToContainerAsync(Stream stream, string destinationStorageContainer, string destinationPath);
    Task CreateContainerIfNotExistsAsync(string destinationStorageContainer);
    Task CopyAllFilesToContainerAsync(
        string sourceStorageContainer,
        string destinationStorageContainer,
        string destinationFolderPath,
        string? sourcePrefix = null,
        Func<BlobItem, bool>? filterFunc = null);

    Task DeleteAllFilesInContainerAsync(string containerName, string? sourcePrefix);
}