#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
USER app
WORKDIR /app
EXPOSE 8080
EXPOSE 8081

ENV TZ "Europe/Amsterdam"

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src

## Start: Authenticate with custom feed ##
ARG FEED_ACCESSTOKEN
ENV VSS_NUGET_EXTERNAL_FEED_ENDPOINTS \
    "{\"endpointCredentials\": [{\"endpoint\":\"https://pkgs.dev.azure.com/pondresnl/Omnia/_packaging/Omnia/nuget/v3/index.json\", \"username\":\"docker\", \"password\":\"${FEED_ACCESSTOKEN}\"}]}"

RUN curl -L https://raw.githubusercontent.com/Microsoft/artifacts-credprovider/master/helpers/installcredprovider.sh  | bash
## End: Authenticate with custom feed ##

COPY ["src/nuget.config", "."]
COPY ["src/Pondres.Omnia.Ingestion.Api.Internal/Pondres.Omnia.Ingestion.Api.Internal.csproj", "src/Pondres.Omnia.Ingestion.Api.Internal/"]
COPY ["src/Common/Pondres.Omnia.Ingestion.Common/Pondres.Omnia.Ingestion.Common.csproj", "src/Common/Pondres.Omnia.Ingestion.Common/"]
COPY ["src/Common/Pondres.Omnia.Ingestion.V2.Contracts/Pondres.Omnia.Ingestion.V2.Contracts.csproj", "src/Common/Pondres.Omnia.Ingestion.V2.Contracts/"]
RUN dotnet restore "./src/Pondres.Omnia.Ingestion.Api.Internal/Pondres.Omnia.Ingestion.Api.Internal.csproj"
COPY . .
WORKDIR "/src/src/Pondres.Omnia.Ingestion.Api.Internal"
RUN dotnet build "./Pondres.Omnia.Ingestion.Api.Internal.csproj" -c "$BUILD_CONFIGURATION" -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./Pondres.Omnia.Ingestion.Api.Internal.csproj" -c "$BUILD_CONFIGURATION" -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Pondres.Omnia.Ingestion.Api.Internal.dll"]