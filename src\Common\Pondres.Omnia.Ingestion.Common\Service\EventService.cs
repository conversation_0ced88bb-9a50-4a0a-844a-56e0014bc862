﻿using MassTransit;
using Newtonsoft.Json;
using Pondres.Omnia.Ingestion.V2.Contracts.Events;
using System;
using System.Threading.Tasks;

namespace Pondres.Omnia.Ingestion.Common.Service;

public class EventService(IBus bus) : IEventService
{
    public async Task PublishIngestionFailureAsync(
        Exception exception,
        string requestId,
        string flowName,
        string customer) => 
        await PublishIngestionFailureAsync(
            message: $"{exception.Message}||{exception.InnerException?.Message}",
            data: JsonConvert.SerializeObject(exception),
            requestId: requestId,
            flowName: flowName,
            customer: customer);

    public async Task PublishIngestionFailureAsync(
        string message,
        string data,
        string requestId,
        string flowName,
        string customer)
    {
        await bus.Publish(new IngestionFailure
        {
            Message = message,
            ExceptionData = data,
            Customer = customer,
            Flow = flowName,
            RequestId = requestId,
            Timestamp = DateTimeOffset.Now
        });
    }

    public async Task PublishIngestionValidationFailedAsync(
        FluentValidation.Results.ValidationResult result,
        string requestId,
        string flowName,
        string customer)
    {
        await bus.Publish(new IngestionValidationFailed
        {
            Message = "Validation failed",
            ExceptionData = result.ToString(),
            Customer = customer,
            Flow = flowName,
            RequestId = requestId,
            Timestamp = DateTimeOffset.Now
        });
    }
}