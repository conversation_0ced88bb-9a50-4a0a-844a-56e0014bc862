﻿using FluentValidation.Results;
using System;
using System.Threading.Tasks;

namespace Pondres.Omnia.Ingestion.Common.Service;

public interface IEventService
{
    Task PublishIngestionFailureAsync(Exception exception, string requestId, string flowName, string customer);
    Task PublishIngestionFailureAsync(string message, string data, string requestId, string flowName, string customer);
    Task PublishIngestionValidationFailedAsync(ValidationResult result, string requestId, string flowName, string customer);
}