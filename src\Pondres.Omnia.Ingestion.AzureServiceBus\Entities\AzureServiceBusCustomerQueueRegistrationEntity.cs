﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Pondres.Omnia.Ingestion.Common.Model;

namespace Pondres.Omnia.Ingestion.AzureServiceBus.Entities;

public class AzureServiceBusCustomerQueueRegistrationEntity
{
    public AzureServiceBusCustomerQueueRegistrationEntity(
        string flowName, 
        string connectionStringSecretName, 
        string queueName, CustomerReferenceDeterminationType customerReferenceDeterminationType, 
        string customerReferenceDeterminationValue)
    {
        FlowName = flowName;
        ConnectionStringSecretName = connectionStringSecretName;
        QueueName = queueName;
        CustomerReferenceDeterminationType = customerReferenceDeterminationType;
        CustomerReferenceDeterminationValue = customerReferenceDeterminationValue;
    }

    [JsonProperty("flowName")]
    public string FlowName { get; set; }

    [JsonProperty("connectionStringSecretName")]
    public string ConnectionStringSecretName { get; set; } 

    [JsonProperty("queueName")]
    public string QueueName { get; set; }

    [JsonProperty("customerReferenceDeterminationType")]
    [JsonConverter(typeof(StringEnumConverter))]
    public CustomerReferenceDeterminationType CustomerReferenceDeterminationType { get; set; } 

    [JsonProperty("customerReferenceDeterminationValue")]
    public string CustomerReferenceDeterminationValue { get; set; } 
}
