﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<IsPackable>false</IsPackable>
		<UserSecretsId>2cd6ce1c-8307-4711-a723-5515c686771c</UserSecretsId>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Azure.Storage.Blobs" Version="12.25.0" />
		<PackageReference Include="FluentAssertions" Version="[7.0.0]" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.8" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
		<PackageReference Include="Serilog.Extensions.Logging" Version="9.0.2" />
		<PackageReference Include="xunit" Version="2.9.3" />
		<PackageReference Include="xunit.runner.visualstudio" Version="3.1.3">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="coverlet.collector" Version="6.0.4">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="Testcontainers.Azurite" Version="4.6.0" />
		<PackageReference Include="Testcontainers.Redis" Version="4.6.0" />
		<PackageReference Include="Testcontainers" Version="4.6.0" />
		<PackageReference Include="Testcontainers.RabbitMq" Version="4.6.0" />
		<PackageReference Include="Moq" Version="4.20.72" />
		<PackageReference Include="NSwag.ApiDescription.Client" Version="14.5.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
	</ItemGroup>
	
	<ItemGroup>
		<OpenApiReference Include="OpenApis\internal_ingeestion_swagger.json" CodeGenerator="NSwagCSharp" Namespace="Pondres.Omnia.Ingestion.Tests.Clients" ClassName="InternalIngestionApiClient">
			<NSwagGenerateExceptionClasses>true</NSwagGenerateExceptionClasses>
			<Options>/OperationGenerationMode:SingleClientFromPathSegments</Options>
		</OpenApiReference>
	</ItemGroup>
	
	<ItemGroup>
		<AssemblyAttribute Include="Xunit.CollectionBehaviorAttribute">
			<_Parameter1>DisableTestParallelization = true</_Parameter1>
			<_Parameter1_IsLiteral>true</_Parameter1_IsLiteral>
		</AssemblyAttribute>
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Pondres.Omnia.Ingestion.Amazon\Pondres.Omnia.Ingestion.Amazon.csproj" />
		<ProjectReference Include="..\Pondres.Omnia.Ingestion.Api.External\Pondres.Omnia.Ingestion.Api.External.csproj" />
		<ProjectReference Include="..\Pondres.Omnia.Ingestion.Api.Internal\Pondres.Omnia.Ingestion.Api.Internal.csproj" />
		<ProjectReference Include="..\Pondres.Omnia.Ingestion.AzureServiceBus\Pondres.Omnia.Ingestion.AzureServiceBus.csproj" />
		<ProjectReference Include="..\Pondres.Omnia.Ingestion.SendGrid\Pondres.Omnia.Ingestion.SendGrid.csproj" />
		<ProjectReference Include="..\Pondres.Omnia.Ingestion.Shopify\Pondres.Omnia.Ingestion.Shopify.csproj" />
	</ItemGroup>
</Project>
