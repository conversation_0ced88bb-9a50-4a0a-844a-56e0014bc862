﻿using Pondres.Omnia.Ingestion.Common.Extensions;
using Pondres.Omnia.Ingestion.SendGrid.Commands;
using Pondres.Omnia.Ingestion.SendGrid.Config;
using Pondres.Omnia.Ingestion.SendGrid.Consumers;
using Solude.ApiBase.Extensions;

namespace Pondres.Omnia.Ingestion.SendGrid;

public class Startup(IHostEnvironment environment, IConfiguration configuration)
{
    private readonly SendGridAppSettings appSettings = configuration.Get<SendGridAppSettings>() ??
       throw new InvalidOperationException("AppSettings not bindable");

    public void Configure(IApplicationBuilder app)
    {
        app.UseDefaultApiDependencies(Program.Name, appSettings);
    }

    public void ConfigureServices(IServiceCollection services)
    {
        services.AddDefaultApiDependencies(appSettings, Program.Name, auth => auth.RequiredToken = appSettings.SendGridIngestionAuthToken);

        services.AddCommonDependencies(
            environment: environment,
            appSettings: appSettings,
            configureBus: configure =>
            {
                configure.AddConsumer<SendGridBulkIngestionConsumer>(typeof(SendGridBulkIngestionConsumerDefinition));
            });

        services.AddScoped<IngestBulkOrderForTriggerFileCommandHandler>();
    }
}
