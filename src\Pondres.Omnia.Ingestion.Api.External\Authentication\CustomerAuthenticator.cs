﻿using Pondres.Omnia.Ingestion.Common.Provider;
using Pondres.Omnia.Ingestion.Common.Service;

namespace Pondres.Omnia.Ingestion.Api.External.Authentication;

public class CustomerAuthenticator(KeyVaultSecretProvider secretProvider, CustomerProvider customerProvider)
{
    public async Task<bool> AuthenticateAsync(string? customer, string? token)
    {
        if (string.IsNullOrEmpty(token) || string.IsNullOrEmpty(customer))
        {
            return false;
        }

        try
        {
            var customerDetails = await customerProvider.GetCustomerDetailsAsync(customer);
            var correctToken = await secretProvider.GetSecretAsync(customerDetails.Customer.VaultName, "ingestion-api-key");
            return correctToken == token;
        }
        catch (InvalidOperationException)
        {
            return false;
        }




    }
}