﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<UserSecretsId>0b0d2a21-617b-4cc2-85bc-45fb13c9b5aa</UserSecretsId>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<DockerfileContext>..\..</DockerfileContext>
		<DockerComposeProjectPath>..\..\docker-compose.dcproj</DockerComposeProjectPath>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.22.0" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.3" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Common\Pondres.Omnia.Ingestion.Common\Pondres.Omnia.Ingestion.Common.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Config\" />
	</ItemGroup>

</Project>
