﻿using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Cosmos.Linq;
using Pondres.Omnia.Ingestion.Amazon.Entities;
using Pondres.Omnia.Ingestion.Common.Config;

namespace Pondres.Omnia.Ingestion.Amazon.Queues;

public class QueueQueries(CosmosClient client)
{
    public async Task<List<AwsQueueRegistrationsEntity>> GetActiveQueuesAsync()
    {
        var configurationContainer = client.GetDatabase(CosmosConfiguration.IngestionDatabaseName).GetContainer(CosmosConfiguration.ConfigurationContainerName);

        var iterator = configurationContainer.GetItemLinqQueryable<AwsQueueRegistrationsEntity>()
            .Where(x => x.Type == CosmosConfiguration.ConfigurationAwsQueueTypeName && x.Active)
            .ToFeedIterator();

        var entities = new List<AwsQueueRegistrationsEntity>();

        while (iterator.HasMoreResults)
        {
            var response = await iterator.ReadNextAsync();
            entities.AddRange(response);
        }

        return entities;
    }
}
