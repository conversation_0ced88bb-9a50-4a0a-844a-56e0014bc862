using System.ComponentModel.DataAnnotations;
using System.Text.Json;
using FluentValidation;
using MassTransit;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Pondres.Omnia.Ingestion.Api.External.Models;
using Pondres.Omnia.Ingestion.Api.External.Validation;
using Pondres.Omnia.Ingestion.V2.Contracts.Events;
using ValidationException = FluentValidation.ValidationException;

namespace Pondres.Omnia.Ingestion.Api.External.Controllers;

[ApiController]
[Route("trigger")]
public class ExternalTriggerController(IBus bus, ILogger<ExternalTriggerController> logger) : ControllerBase
{
    [HttpPost]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> CreateExternalTriggerAsync(
        [FromHeader(Name = "x-customer")][Required] string customer,
        [FromHeader(Name = "x-customer-reference")][Required] string customerReference,
        [FromBody] TriggerRequestModel body)
    {
        var requestId = Guid.NewGuid().ToString();

        using var _ = logger.BeginScope("Create external trigger request received for customer {Customer}", requestId, customer);

        try
        {
            await ValidateBodyAsync(body);

            var jsonDataString = body.JsonFileData is null ? string.Empty : JsonSerializer.Serialize(body.JsonFileData);

            await bus.Publish(new ExternalTriggerReceived
            {
                Customer = customer,
                OrderReference = customerReference,
                SubType = body.SubType!,
                Timestamp = DateTime.UtcNow,
                JsonFileData = jsonDataString
            });

            return StatusCode(StatusCodes.Status204NoContent);
        }
        catch (ValidationException ex)
        {
            logger.LogError(ex, "ValidationException during handling of request, RequestId '{RequestId}', OrderReference '{OrderReference}', Customer {Customer}", requestId, customerReference, customer);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "UnexpectedException during handling of request, RequestId '{RequestId}', OrderReference '{OrderReference}', Customer {Customer}", requestId, customerReference, customer);
            return StatusCode(StatusCodes.Status500InternalServerError);
        }
    }

    private static async Task ValidateBodyAsync(TriggerRequestModel body)
    {
        if (body == null)
            throw new ValidationException("Request body is required");

        var validator = new TriggerRequestModelValidator();
        await validator.ValidateAndThrowAsync(body);
    }
}

