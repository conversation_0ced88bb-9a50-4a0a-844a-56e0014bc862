﻿using Microsoft.Extensions.Caching.Memory;
using Pondres.Omnia.Customer.Contracts.Request;
using Solude.CustomerProvider;
using System;
using System.Threading.Tasks;

namespace Pondres.Omnia.Ingestion.Common.Service;

public class CustomerProvider(
    IMemoryCache cache,
    ICustomerRequestSender requestSender)
{
    public async Task<GetCustomerResponse> GetCustomerDetailsAsync(string customer)
    {
        if (!cache.TryGetValue(customer, out GetCustomerResponse? customerResponse) || customerResponse == null)
        {
            customerResponse = await GetCustomerAndCacheAsync(customer);

            cache.Set(customer, customerResponse, TimeSpan.FromMinutes(10));
        }

        return customerResponse;
    }

    private async Task<GetCustomerResponse> GetCustomerAndCacheAsync(string customer)
    {
        var request = new GetCustomerRequest() { Id = customer };

        var response = await requestSender.SendBusRequestAsync<GetCustomerRequest, GetCustomerResponse>(request);

        return !response.Success ?
            throw new InvalidOperationException($"Failed to get customer {customer}: {response.Error}") :
            response;
    }
}
