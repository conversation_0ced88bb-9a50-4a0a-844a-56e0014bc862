﻿using System;

namespace Pondres.Omnia.Ingestion.V2.Contracts.Events
{
    public class IngestionValidationFailed
    {
        public string Customer { get; set; }
        public string Flow { get; set; }

        public string ExceptionData { get; set; }
        public string Message { get; set; }

        public string RequestId { get; set; }

        public DateTimeOffset Timestamp { get; set; }
    }
}