﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Pondres.Omnia.Ingestion.Api.External;
using Solude.CustomerProvider;
using Pondres.Omnia.Ingestion.Tests.Fixtures.Data;

namespace Pondres.Omnia.Ingestion.Tests.Fixtures;

public class ExternalApiAppFixture(
    ContainersFixture containers,
    string cosmosDbEndpoint) : BaseAppFixture<Program>(containers)
{
    private static readonly Mock<ICustomerRequestSender> customerRequestSender = new();
    public CustomerRequestSenderFixture CustomerRequestSender { get; private set; } = new(customerRequestSender);

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureAppConfiguration((context, builder) =>
        {
            var configuration = new Dictionary<string, string?>
            {
                // Overwrite configuration with connectionstrings to the created services
                { "DataSeedDisabled", "true" },
                { "AzuriteConnectionString", Containers.Azurite.GetConnectionString() },
                { "RabbitConnectionString", Containers.RabbitMq.GetConnectionString() },
                { "CosmosDbEndpoint", cosmosDbEndpoint },
                { "IsDevelopment", "true" }
            };

            builder.AddInMemoryCollection(configuration);
        });
        builder.ConfigureTestServices(services =>
        {
            services.Remove(services.Single(x => x.ServiceType == typeof(ICustomerRequestSender)));
            services.AddSingleton(customerRequestSender.Object);
        });

        builder.UseEnvironment("Development");
    }
}
