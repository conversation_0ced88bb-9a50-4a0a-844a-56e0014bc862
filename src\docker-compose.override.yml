version: '3.4'

services:
  pondres.omnia.ingestion.amazon:
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_HTTP_PORTS=8080
      - ASPNETCORE_HTTPS_PORTS=8081
    ports:
      - "5031:8080"
      - "5030:8081"
    volumes:
      - ${APPDATA}/Microsoft/UserSecrets:/home/<USER>/.microsoft/usersecrets:ro
      - ${APPDATA}/ASP.NET/Https:/home/<USER>/.aspnet/https:ro
      
  pondres.omnia.ingestion.azureservicebus:
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_HTTP_PORTS=8080
      - ASPNETCORE_HTTPS_PORTS=8081
    ports:
      - "5010:8080"
      - "5011:8081"
    volumes:
      - ${APPDATA}/Microsoft/UserSecrets:/home/<USER>/.microsoft/usersecrets:ro
      - ${APPDATA}/ASP.NET/Https:/home/<USER>/.aspnet/https:ro

  pondres.omnia.ingestion.internalapi:
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_HTTP_PORTS=8080
      - ASPNETCORE_HTTPS_PORTS=8081
    ports:
      - "5020:8080"
      - "5021:8081"
    volumes:
      - ${APPDATA}/Microsoft/UserSecrets:/home/<USER>/.microsoft/usersecrets:ro
      - ${APPDATA}/ASP.NET/Https:/home/<USER>/.aspnet/https:ro


  pondres.omnia.ingestion.externalapi:
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_HTTP_PORTS=8080
      - ASPNETCORE_HTTPS_PORTS=8081
    ports:
      - "5022:8080"
      - "5023:8081"
    volumes:
      - ${APPDATA}/Microsoft/UserSecrets:/home/<USER>/.microsoft/usersecrets:ro
      - ${APPDATA}/ASP.NET/Https:/home/<USER>/.aspnet/https:ro

  pondres.omnia.ingestion.sendgrid:
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_HTTP_PORTS=8080
      - ASPNETCORE_HTTPS_PORTS=8081
    ports:
      - "5040:8080"
      - "5041:8081"
    volumes:
      - ${APPDATA}/Microsoft/UserSecrets:/home/<USER>/.microsoft/usersecrets:ro
      - ${APPDATA}/ASP.NET/Https:/home/<USER>/.aspnet/https:ro

  pondres.omnia.ingestion.shopify:
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_HTTP_PORTS=8080
      - ASPNETCORE_HTTPS_PORTS=8081
    ports:
      - "5050:8080"
      - "5051:8081"
    volumes:
      - ${APPDATA}/Microsoft/UserSecrets:/home/<USER>/.microsoft/usersecrets:ro
      - ${APPDATA}/ASP.NET/Https:/home/<USER>/.aspnet/https:ro