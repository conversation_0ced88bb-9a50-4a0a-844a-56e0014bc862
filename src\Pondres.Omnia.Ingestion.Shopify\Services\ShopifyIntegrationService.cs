﻿using Newtonsoft.Json;
using Pondres.Omnia.Ingestion.Common.Model;
using Pondres.Omnia.Ingestion.Common.Provider;
using Pondres.Omnia.Ingestion.Common.Service;
using Pondres.Omnia.Ingestion.Shopify.Constants;
using Pondres.Omnia.Ingestion.Shopify.Entities;
using Serilog;
using ShopifySharp;
using ShopifySharp.Filters;
using StackExchange.Redis;
using System.Text;

namespace Pondres.Omnia.Ingestion.Shopify.Services;

public class ShopifyIntegrationService(
    IHttpClientFactory httpClientFactory,
    ISecretProvider secretProvider,
    CustomerProvider customerProvider,
    Common.Service.IOrderService orderService,
    Common.Service.IEventService eventService,
    IConnectionMultiplexer redis)
{
    private readonly IDatabase cache = redis.GetDatabase();

    public async Task ImportAllAssignedFulfillmentOrdersAsync(ShopifyEnvironmentEntity environment)
    {
        var requestId = Guid.NewGuid().ToString();

        var customerDetails = await customerProvider.GetCustomerDetailsAsync(environment.Customer);

        var assignedFulfillmentOrders = await FetchAllAssignedFulfillmentOrdersAsync(
            environment: environment,
            vaultName: customerDetails.Customer.VaultName);

        foreach (var fulfillment in assignedFulfillmentOrders)
        {
            try
            {
                var cacheKey = $"{ShopifyConstants.CacheKeyPrefix}{environment.Customer}_{fulfillment.Id}";
                var isProcessed = await cache.StringGetAsync(cacheKey);

                if (isProcessed.IsNullOrEmpty)
                {
                    await CreateShopifyOrderAsync(environment,
                        fulfillmentOrder: fulfillment,
                        requestId: requestId,
                        storageAccountName: customerDetails.Customer.StorageAccountName);

                    await cache.StringSetAsync(cacheKey, "processed", TimeSpan.FromDays(1));

                    // Shopify rate limit is 2 orders per second. This will ensure we are under it.
                    await Task.Delay(ShopifyConstants.ShopifyRateLimitDelay);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Failed to fetch orders from Shopify for environment {Environment}", environment.Customer);
                await eventService.PublishIngestionFailureAsync(ex, requestId, environment.FlowName, environment.Customer);
            }
        }
    }

    private async Task<List<AssignedFulfillmentOrder>> FetchAllAssignedFulfillmentOrdersAsync(
        ShopifyEnvironmentEntity environment,
        string vaultName)
    {
        var url = await secretProvider.GetSecretAsync(vaultName, environment.ShopifyUrlSecretName);
        var accessToken = await secretProvider.GetSecretAsync(vaultName, environment.ShopifyAccessTokenSecretName);

        var shopifyService = new AssignedFulfillmentOrderService(url, accessToken);

        shopifyService.SetHttpClient(httpClientFactory.CreateClient("Shopify"));

        var filter = new AssignedFulfillmentOrderFilter { AssignmentStatus = ShopifyConstants.ShopifyFulfillmentRequestedStatus };

        var allAssignedFulfillments = new List<AssignedFulfillmentOrder>();
        var initialRequest = await shopifyService.ListAsync(filter);
        allAssignedFulfillments.AddRange(initialRequest.Items);

        var hasNextPage = initialRequest.HasNextPage;
        var nextPageFilter = initialRequest.GetNextPageFilter();

        while (hasNextPage)
        {
            var nextPage = await shopifyService.ListAsync(nextPageFilter);
            allAssignedFulfillments.AddRange(nextPage.Items);

            hasNextPage = nextPage.HasNextPage;
            nextPageFilter = nextPage.GetNextPageFilter();
        }

        return allAssignedFulfillments;
    }

    private async Task CreateShopifyOrderAsync(
        ShopifyEnvironmentEntity environment,
        AssignedFulfillmentOrder fulfillmentOrder,
        string requestId,
        string storageAccountName)
    {
        var createOrderModel = new OrderCreationModel(
            CustomerReference: fulfillmentOrder.Id?.ToString() ?? "",
            Categories: new OrderCreationModelCategories { One = "" },
            CustomerStorageAccountName: storageAccountName,
            Customer: environment.Customer,
            FileName: $"{environment.Customer}_{fulfillmentOrder.Id}.json",
            FlowName: environment.FlowName,
            FlowId: null,
            OrderId: Guid.NewGuid(),
            RequestId: requestId,
            SourceName: "Shopify");

        using var orderStream = new MemoryStream(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(fulfillmentOrder)));

        await orderService.CreateSingleRawDataFileOrderAsync(orderStream, createOrderModel);
    }
}
