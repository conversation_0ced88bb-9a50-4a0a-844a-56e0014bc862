﻿using Microsoft.Azure.Cosmos;
using Pondres.Omnia.Ingestion.AzureServiceBus.Entities;
using Pondres.Omnia.Ingestion.Common.Config;
using Pondres.Omnia.Ingestion.Common.Model;
using Pondres.Omnia.Ingestion.Shopify.Entities;

namespace Pondres.Omnia.Ingestion.Tests.Fixtures.Data;

public class ConfigurationFixture(
    ITestDependenciesProvider dependenciesProvider, 
    string serviceBusConnectionString)
{
    public static string ServiceBusSecretName => "AzureServiceBusConnectionString";

    public async Task<AzureServiceBusQueueRegistrationsEntity> CreateAzureServiceBusConfigurationAsync(
        string customer, 
        string flowName = "AzureServiceBus",
        string determinationValue = $".CustomerReference",
        CustomerReferenceDeterminationType determinationType = CustomerReferenceDeterminationType.JsonPath)
    {
        var entity = new AzureServiceBusQueueRegistrationsEntity(
            customer: customer,
            vaultName: customer.Replace("-", "_"),
            storageAccountName: "devstoreaccount1");

        var queue = new AzureServiceBusCustomerQueueRegistrationEntity(
            flowName: flowName,
            connectionStringSecretName: ServiceBusSecretName,
            queueName: AzureServiceBusFixture.TestCustomerQueueName,
            customerReferenceDeterminationType: determinationType,
            customerReferenceDeterminationValue: determinationValue);

        entity.Queues.Add(queue);

        var cosmosClient = dependenciesProvider.ResolveAzureServiceBusService<CosmosClient>();

        var configurationContainer = cosmosClient
           .GetDatabase(CosmosConfiguration.IngestionDatabaseName)
           .GetContainer(CosmosConfiguration.ConfigurationContainerName);

        await configurationContainer.CreateItemAsync(entity);

        Environment.SetEnvironmentVariable(
            variable: $"SECRET_{entity.VaultName}_{ServiceBusSecretName}",
            value: serviceBusConnectionString);

        return entity;
    }

    public async Task<ShopifyEnvironmentEntity> CreateShopifyConfigurationAsync(string customer, string flowName = "Shopify")
    {
        var entity = new ShopifyEnvironmentEntity(
            customer: customer,
            shopifyAccessTokenSecretName: "ShopifyAccessToken",
            shopifyUrlSecretName: "ShopifyUrl",
            flowName: flowName);

        var cosmosClient = dependenciesProvider.ResolveShopifyService<CosmosClient>();

        var configurationContainer = cosmosClient
           .GetDatabase(CosmosConfiguration.IngestionDatabaseName)
           .GetContainer(CosmosConfiguration.ConfigurationContainerName);

        await configurationContainer.CreateItemAsync(entity);

        return entity;
    }
}
