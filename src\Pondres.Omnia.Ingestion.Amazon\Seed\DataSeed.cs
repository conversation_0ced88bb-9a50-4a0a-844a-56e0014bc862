﻿using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Options;
using Pondres.Omnia.Ingestion.Amazon.Entities;
using Pondres.Omnia.Ingestion.Common.Config;
using Pondres.Omnia.Ingestion.Common.Model;
using Solude.ApiBase.Contracts;

namespace Pondres.Omnia.Ingestion.Amazon.Seed;

public class DataSeedSettings
{
    public string StorageAccountName { get; set; } = string.Empty;
    public string KeyVaultName { get; set; } = string.Empty;
    public string CustomerQueueUrl { get; set; } = string.Empty;
}

public class DataSeed(CosmosClient cosmosClient, IOptions<DataSeedSettings> options) : IDataSeed
{
    private readonly DataSeedSettings settings = options.Value;

    public async Task SeedAsync()
    {
        var registrationsEntity = new AwsQueueRegistrationsEntity(
            customer: "K00021",
            vaultName: settings.KeyVaultName,
            storageAccountName: settings.StorageAccountName);

        registrationsEntity.Queues.Add(new AwsCustomerQueueRegistrationEntity(
            queueUrl: settings.CustomerQueueUrl,
            flowName: "AwsSqs",
            accessKeySecretName: "AwsSqsAccessKey",
            secretKeySecretName: "AwsSqsSecretKey",
            customerReferenceDeterminationType: CustomerReferenceDeterminationType.JsonPath,
            customerReferenceDeterminationTypeValue: "$.consignmentId"));

        var container = cosmosClient.GetContainer(CosmosConfiguration.IngestionDatabaseName, CosmosConfiguration.ConfigurationContainerName);

        try
        {
            await container.CreateItemAsync(registrationsEntity);
        }
        catch (CosmosException exception) when (exception.StatusCode == System.Net.HttpStatusCode.Conflict)
        {
            await container.ReplaceItemAsync(registrationsEntity, id: registrationsEntity.Customer, new PartitionKey(registrationsEntity.Type));
        }
    }
}
