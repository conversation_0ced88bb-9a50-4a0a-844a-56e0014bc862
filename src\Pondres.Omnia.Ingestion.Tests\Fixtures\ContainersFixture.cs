﻿using DotNet.Testcontainers.Containers;
using Microsoft.Extensions.Logging;
using Serilog;
using Testcontainers.Azurite;
using Testcontainers.RabbitMq;
using Testcontainers.Redis;

namespace Pondres.Omnia.Ingestion.Tests.Fixtures;

public sealed class ContainersFixture : IDisposable
{
    public RabbitMqContainer RabbitMq { get; }
    public AzuriteContainer Azurite { get; }
    public RedisContainer Redis { get; }

    private readonly IContainer[] containers;

    public ContainersFixture()
    {
        var loggerFactory = LoggerFactory.Create(builder => builder.AddSerilog());
        var logger = loggerFactory.CreateLogger("TestContainers");

        RabbitMq = new RabbitMqBuilder().WithImage("masstransit/rabbitmq").WithLogger(logger).Build();
        Azurite = new AzuriteBuilder().WithImage("mcr.microsoft.com/azure-storage/azurite:3.35.0").WithLogger(logger).Build();
        Redis = new RedisBuilder().WithLogger(logger).Build();

        containers = [RabbitMq, Azurite, Redis];
    }

    public async void Dispose() => await DisposeAllAsync();

    public async Task DisposeAllAsync() => await Task.WhenAll(containers.Select(x => x.DisposeAsync().AsTask()));

    public async Task StartAllAsync() => await Task.WhenAll(containers.Select(x => x.StartAsync()));
}
