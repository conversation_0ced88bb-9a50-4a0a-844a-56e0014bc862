﻿using FluentAssertions;
using FluentAssertions.Execution;
using FluentAssertions.Primitives;
using MassTransit;
using Pondres.Omnia.Ingestion.Tests.Helper;
using System.Collections.Concurrent;
using System.Linq.Expressions;

namespace Pondres.Omnia.Ingestion.Tests.Consumer;
public class GlobalConsumeObserver : IConsumeObserver
{
    public ConcurrentDictionary<object, TaskCompletionSource<object>> Messages { get; } = new();

    public Task ConsumeFault<T>(ConsumeContext<T> context, Exception exception) where T : class
    {
        var source = Messages.GetOrAdd(context.Message, new TaskCompletionSource<object>());
        source.SetException(exception);

        return Task.CompletedTask;
    }

    public Task PostConsume<T>(ConsumeContext<T> context) where T : class
    {
        var source = Messages.GetOrAdd(context.Message, new TaskCompletionSource<object>());
        source.SetResult(context.Message);

        return Task.CompletedTask;
    }

    public Task PreConsume<T>(ConsumeContext<T> context) where T : class
    {
        Messages.TryAdd(context.Message, new TaskCompletionSource<object>());
        return Task.CompletedTask;
    }

    public TestConsumeObserverAssertions Should() => new(this);
}

public class TestConsumeObserverAssertions(GlobalConsumeObserver instance) :
    ReferenceTypeAssertions<GlobalConsumeObserver, TestConsumeObserverAssertions>(instance)
{
    protected override string Identifier => "publisher";

    public async Task<TMessage> HaveSeenMessageAsync<TMessage>(
        Expression<Func<TMessage, bool>> messageExpression, string because = "", params object[] becauseArgs)
    {
        var messages = await HaveSeenMessagesAsync(messageExpression, 1, because, becauseArgs);

        return messages.Single();
    }

    public async Task<List<TMessage>> HaveSeenMessagesAsync<TMessage>(
        Expression<Func<TMessage, bool>> messageExpression, int expectedCount, string because = "", params object[] becauseArgs)
    {
        var messageMatchFunc = messageExpression.Compile();

        try
        {
            await TestHelper.WaitForAsync(
                TimeSpan.FromSeconds(5),
                () => Task.FromResult(Subject.Messages.Keys.OfType<TMessage>().Count(messageMatchFunc).Should().Be(expectedCount)));
        }
        catch (Exception)
        {
            Execute.Assertion
                .BecauseOf(because, becauseArgs)
                .FailWith("Found {0} messages where {1}{reason}, but expected {2}." + Environment.NewLine + Environment.NewLine + "{3}.",
                    Subject.Messages.Keys.OfType<TMessage>().Count(messageMatchFunc), messageExpression, expectedCount, Subject.Messages.Keys);
        }

        var matchedMessages = Subject.Messages.Keys.OfType<TMessage>().Where(messageMatchFunc).ToList();

        var tasks = matchedMessages.Select(x => Subject.Messages[x!].Task).ToArray();

        await Task.WhenAll(tasks);

        Execute.Assertion
            .BecauseOf(because, becauseArgs)
            .ForCondition(matchedMessages.Count == expectedCount)
            .FailWith("Found {0} messages where {1}{reason}, but expected {2}." + Environment.NewLine + Environment.NewLine + "{3}.",
                matchedMessages.Count, messageExpression, expectedCount, Subject);

        return matchedMessages!;
    }
}
