﻿using Newtonsoft.Json;

namespace Pondres.Omnia.Ingestion.SendGrid.Models;

public class BlobData
{
    [JsonProperty("api")]
    public string Api { get; set; } = string.Empty;

    [JsonProperty("clientRequestId")]
    public string ClientRequestId { get; set; } = string.Empty;

    [JsonProperty("requestId")]
    public string RequestId { get; set; } = string.Empty;

    [JsonProperty("eTag")]
    public string ETag { get; set; } = string.Empty;

    [JsonProperty("contentType")]
    public string ContentType { get; set; } = string.Empty;

    [JsonProperty("contentLength")]
    public long ContentLength { get; set; }

    [JsonProperty("blobType")]
    public string BlobType { get; set; } = string.Empty;

    [JsonProperty("accessTier", NullValueHandling = NullValueHandling.Ignore)]
    public string? AccessTier { get; set; }

    [JsonProperty("url")]
    public string Url { get; set; } = string.Empty;

    [JsonProperty("sequencer", NullValueHandling = NullValueHandling.Ignore)]
    public string? Sequencer { get; set; }

    [JsonProperty("storageDiagnostics")]
    public StorageDiagnostics StorageDiagnostics { get; set; } = new();
}
