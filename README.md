# Introduction 
Version 2 of ingestion projects where we 'self-host' them inside the K8s cluster.

# Structure
Deploy - Deployment artifacts/files used in the Deployment

Src - Source code
Src/Common - All common code used by 

# AWS Ingestion
First version currently in the app that loops through the configurations for each customer, its possible to make multiple queue's per customer to read.
Queue's will need 2 secrets for the IAM user in AWS to read them, the access secret and secret key.

Queue's are being read sequentially via a BackgroundService. Into the future if we have more AWS queue's to read we probably need a continuous process per each queue we want to read and manage this. For now this simple arrangement should be enough.

## Test queue location
https://eu-north-1.console.aws.amazon.com/sqs/v3/home?region=eu-north-1#/queues/https%3A%2F%2Fsqs.eu-north-1.amazonaws.com%2F058264124862%2Fpon-drs-test
