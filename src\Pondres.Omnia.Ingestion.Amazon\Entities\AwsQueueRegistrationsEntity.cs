﻿using Newtonsoft.Json;
using Pondres.Omnia.Ingestion.Common.Config;

namespace Pondres.Omnia.Ingestion.Amazon.Entities;

public class AwsQueueRegistrationsEntity
{
    public AwsQueueRegistrationsEntity(string customer, string vaultName, string storageAccountName)
    {
        Customer = customer;
        VaultName = vaultName;
        StorageAccountName = storageAccountName;
        Active = true;
    }

    [JsonProperty("id")]
    public string Customer { get; set; }

    [JsonProperty("vaultName")]
    public string VaultName { get; set; }

    [JsonProperty("active")]
    public bool Active { get; set; }

    [JsonProperty("createdUtc")]
    public DateTime CreatedUtc { get; set; } = DateTime.UtcNow;

    [JsonProperty("updatedUtc")]
    public DateTime UpdatedUtc { get; set; } = DateTime.UtcNow;

    [JsonProperty("lastSyncUtc")]
    public DateTime? LastSyncUtc { get; set; }

    [JsonProperty("type")]
    public string Type { get; } = CosmosConfiguration.ConfigurationAwsQueueTypeName;

    [JsonProperty("version")]
    public string Version { get; set; } = "1";

    [JsonProperty("queues")]
    public List<AwsCustomerQueueRegistrationEntity> Queues { get; set; } = [];

    [JsonProperty("storageAccountName")]
    public string StorageAccountName { get; set; }
}
