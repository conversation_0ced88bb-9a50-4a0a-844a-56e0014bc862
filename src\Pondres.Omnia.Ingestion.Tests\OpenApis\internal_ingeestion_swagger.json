﻿{
  "openapi": "3.0.1",
  "info": {
    "title": "Pondres.Omnia.Ingestion.Api.Internal",
    "version": "1.0"
  },
  "servers": [
    {
      "url": "/ingestion/internal"
    }
  ],
  "paths": {
    "/submit": {
      "post": {
        "tags": [
          "InternalIngestion"
        ],
        "parameters": [
          {
            "name": "x-customer-reference",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "x-flow",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "x-flow-id",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "x-customer",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "x-category-one",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "x-category-two",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "x-category-three",
            "in": "header",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "x-token",
            "in": "header",
            "description": "Access token",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "requestBody": {
          "content": {
            "multipart/form-data": {
              "schema": {
                "type": "object",
                "properties": {
                  "content": {
                    "type": "string",
                    "format": "binary"
                  }
                }
              },
              "encoding": {
                "content": {
                  "style": "form"
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/ApiResultDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ApiResultDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/ApiResultDto"
                }
              }
            }
          },
          "400": {
            "description": "Bad Request",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/ApiResultDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ApiResultDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/ApiResultDto"
                }
              }
            }
          },
          "404": {
            "description": "Not Found",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/ApiResultDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ApiResultDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/ApiResultDto"
                }
              }
            }
          },
          "409": {
            "description": "Conflict",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/ApiResultDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ApiResultDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/ApiResultDto"
                }
              }
            }
          }
        }
      }
    }
  },
  "components": {
    "schemas": {
      "ApiResultDto": {
        "type": "object",
        "properties": {
          "success": {
            "type": "boolean"
          },
          "defaultMessage": {
            "type": "string",
            "nullable": true
          },
          "errorCode": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      }
    }
  }
}