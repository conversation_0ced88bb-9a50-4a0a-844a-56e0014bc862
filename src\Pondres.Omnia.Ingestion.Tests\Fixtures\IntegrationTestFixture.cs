﻿using Microsoft.Extensions.Configuration;
using Pondres.Omnia.Ingestion.Tests.Fixtures.Data;
using Serilog;
using Xunit;

namespace Pondres.Omnia.Ingestion.Tests.Fixtures;

[CollectionDefinition("IntegrationTests")]
public class IntegrationTestCollection : ICollectionFixture<IntegrationTestFixture>
{
    // This class has no code, and is never created. Its purpose is simply
    // to be the place to apply [CollectionDefinition] and all the
    // ICollectionFixture<> interfaces.
    // PS: Do not change this because we are testing against queues and it's possible to get test interference
}

public class IntegrationTestFixture : IAsyncLifetime, ITestDependenciesProvider
{
    private readonly string serviceBusConnectionString;

    public ContainersFixture Containers { get; private set; }
    public AzureServiceBusAppFixture AzureServiceBusApp { get; }
    public ShopifyAppFixture ShopifyApp { get; }
    public InternalApiAppFixture InternalApiApp { get; }
    public ExternalApiAppFixture ExternalApiApp { get; }
    public SendGridAppFixture SendGridApp { get; }

    public ConfigurationFixture Configuration { get; set; } = default!;
    public AzureServiceBusFixture ServiceBus { get; set; } = default!;
    public BlobFixture Blob { get; private set; } = default!;

    public IntegrationTestFixture()
    {
        var configuration = new ConfigurationBuilder().AddUserSecrets<AzureServiceBusAppFixture>().AddEnvironmentVariables().Build();

        var cosmosDbEndpoint = configuration["CosmosDbEndpoint"] ??
             throw new InvalidOperationException("CosmosDbEndpoint is not set, add a CosmosDbEndpoint entry in the user secrets of the integration project, or if on build agent put it in the environment settings");

        serviceBusConnectionString = configuration[ConfigurationFixture.ServiceBusSecretName] ??
             throw new InvalidOperationException($"{ConfigurationFixture.ServiceBusSecretName} is not set, add a {ConfigurationFixture.ServiceBusSecretName} entry in the user secrets of the integration project, or if on build agent put it in the environment settings");

        Containers = new ContainersFixture();
        AzureServiceBusApp = new AzureServiceBusAppFixture(Containers, cosmosDbEndpoint);
        InternalApiApp = new InternalApiAppFixture(Containers, cosmosDbEndpoint);
        ExternalApiApp = new ExternalApiAppFixture(Containers, cosmosDbEndpoint);
        ShopifyApp = new ShopifyAppFixture(Containers, cosmosDbEndpoint);
        SendGridApp = new SendGridAppFixture(Containers, cosmosDbEndpoint);

        // Nice to have all the logging of the service during testing
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Warning()
            .WriteTo.Console()
            .WriteTo.File("log.txt")
            .CreateLogger();
    }

    public async Task DisposeAsync()
    {
        await Containers.DisposeAllAsync();

        await Log.CloseAndFlushAsync();
    }

    public async Task InitializeAsync()
    {
        await Containers.StartAllAsync();
        await AzureServiceBusApp.InitializeAsync();
        await InternalApiApp.InitializeAsync();
        await SendGridApp.InitializeAsync();
        await ShopifyApp.InitializeAsync();

        Configuration = new(this, serviceBusConnectionString);
        Blob = new(Containers.Azurite.GetConnectionString());
        ServiceBus = new(this, serviceBusConnectionString);

        await Blob.CreateOrderDataContainerAsync();
    }

    public TServiceType ResolveAzureServiceBusService<TServiceType>() where TServiceType : class => AzureServiceBusApp.ResolveService<TServiceType>();
    public TServiceType ResolveSendGridService<TServiceType>() where TServiceType : class => SendGridApp.ResolveService<TServiceType>();
    public TServiceType ResolveShopifyService<TServiceType>() where TServiceType : class => ShopifyApp.ResolveService<TServiceType>();
}
