# Docker
# Build a Docker image 
# https://docs.microsoft.com/azure/devops/pipelines/languages/docker

trigger:
- main

resources:
- repo: self

variables:
- name: amazonContainerRepository
  value: 'omnia-ingestion-amazon'
- name: amazonDockerFile
  value: $(Build.SourcesDirectory)/src/Pondres.Omnia.Ingestion.Amazon/Dockerfile
- name: sendgridContainerRepository
  value: 'omnia-ingestion-sendgrid'
- name: sendgridDockerFile
  value: $(Build.SourcesDirectory)/src/Pondres.Omnia.Ingestion.SendGrid/Dockerfile
- name: internalApiContainerRepository
  value: 'omnia-ingestion-internalApi'
- name: externalApiContainerRepository
  value: 'omnia-ingestion-externalApi'
- name: internalApiDockerFile
  value: $(Build.SourcesDirectory)/src/Pondres.Omnia.Ingestion.Api.Internal/Dockerfile
- name: externalApiDockerFile
  value: $(Build.SourcesDirectory)/src/Pondres.Omnia.Ingestion.Api.External/Dockerfile
- name: shopifyDockerFile
  value: $(Build.SourcesDirectory)/src/Pondres.Omnia.Ingestion.Shopify/Dockerfile
- name: shopifyContainerRepository
  value: 'omnia-ingestion-shopify'
- name: azureServiceBusContainerRepository
  value: 'omnia-ingestion-azureservicebus'
- name: azureServiceBusDockerFile
  value: $(Build.SourcesDirectory)/src/Pondres.Omnia.Ingestion.AzureServiceBus/Dockerfile
- name: tag
  value: '$(Build.BuildNumber)'
- name: nugetVersion
  value: '1.$(Build.BuildNumber)'
- group: DockerBuild - Nuget PAT TOKEN
- group: Build - Common
- name: sonarProjectKey
  value: 'pondresnl_Pondres.Omnia.Ingestion.V2'
- name: sonarProjectName
  value: 'Pondres.Omnia.Ingestion.V2'
- name: contractPackageProject
  value: $(Build.SourcesDirectory)/src/Common/Pondres.Omnia.Ingestion.V2.Contracts/Pondres.Omnia.Ingestion.V2.Contracts.csproj

pool:
  vmImage: 'ubuntu-latest'

stages:
- stage: BuildAndPublish
  displayName: Build, Test and Publish
  jobs:  
  - job: BuildAndTest
    displayName: Build and Test .NET projects
    steps:
    - task: UseDotNet@2
      displayName: 'Install .NET 9 SDK'
      inputs:
        packageType: 'sdk'
        version: '9.x'
    - task: SonarCloudPrepare@1
      inputs:
        SonarCloud: 'SonarCloud'
        organization: 'pondresnl'
        scannerMode: 'MSBuild'
        projectKey: '$(sonarProjectKey)'
        projectName: '$(sonarProjectName)'
        extraProperties: |
          # Additional properties that will be passed to the scanner, 
          # Put one key=value per line, example:
          # sonar.exclusions=**/*.bin
          sonar.cs.opencover.reportsPaths=$(Agent.TempDirectory)/**/coverage.opencover.xml
    - task: DotNetCoreCLI@2
      displayName: Restore
      inputs:
        command: 'restore'
        projects: '**/*.sln'
        feedsToUse: 'select'
        vstsFeed: '534b90ef-0e1b-475a-8b29-f869c9ffb2fd/202483c3-5d54-4aec-a875-a4bd78523c18'
        verbosityRestore: minimal
    - task: DotNetCoreCLI@2
      displayName: Build
      inputs:
        command: 'build'
        projects: '**/*.sln'
        arguments: '--no-restore'
    - task: DotNetCoreCLI@2
      displayName: Test
      inputs:
        command: 'test'
        projects: '**/*.sln'
        arguments: '--no-build -- xunit.parallelizeAssembly=true'
      env:
        CosmosDbEndpoint: "$(CosmosDbEndpoint)"
        AzureServiceBusConnectionString: "$(AzureServiceBusConnectionString)"
    - task: SonarCloudAnalyze@1
      inputs:
        jdkversion: 'JAVA_HOME_17_X64'
    - task: SonarCloudPublish@1
      inputs:
        pollingTimeoutSec: '300'
          
  - job: BuildandPublishAmazonContainer
    displayName: Build and Publish Amazon Container
    steps:
    - task: UseDotNet@2
      displayName: 'Install .NET 9 SDK'
      inputs:
        packageType: 'sdk'
        version: '9.x'
    - task: Docker@2
      inputs:
        containerRegistry: 'TestContainerRegistry'
        repository: '$(amazonContainerRepository)'
        command: 'build'
        Dockerfile: '$(amazonDockerfile)'
        buildContext: '$(Build.SourcesDirectory)'
        tags: |
          latest
          $(tag)
        arguments: '--build-arg FEED_ACCESSTOKEN=$(FEED_ACCESSTOKEN)'
    - task: Docker@2
      inputs:
        containerRegistry: 'TestContainerRegistry'
        repository: '$(amazonContainerRepository)'
        command: 'push'
        tags: |
          latest
          $(tag)
    - task: Docker@2
      inputs:
        containerRegistry: 'ProdContainerRegistry'
        repository: '$(amazonContainerRepository)'
        command: 'build'
        Dockerfile: '$(amazonDockerfile)'
        buildContext: '$(Build.SourcesDirectory)'
        tags: |
          latest
          $(tag)
        arguments: '--build-arg FEED_ACCESSTOKEN=$(FEED_ACCESSTOKEN)'
    - task: Docker@2
      inputs:
        containerRegistry: 'ProdContainerRegistry'
        repository: '$(amazonContainerRepository)'
        command: 'push'
        tags: |
          latest
          $(tag)
          
  - job: BuildandPublishSendGridContainer
    displayName: Build and Publish SendGrid Container
    steps:
    - task: UseDotNet@2
      displayName: 'Install .NET 9 SDK'
      inputs:
        packageType: 'sdk'
        version: '9.x'
    - task: Docker@2
      inputs:
        containerRegistry: 'TestContainerRegistry'
        repository: '$(sendgridContainerRepository)'
        command: 'build'
        Dockerfile: '$(sendgridDockerfile)'
        buildContext: '$(Build.SourcesDirectory)'
        tags: |
          latest
          $(tag)
        arguments: '--build-arg FEED_ACCESSTOKEN=$(FEED_ACCESSTOKEN)'
    - task: Docker@2
      inputs:
        containerRegistry: 'TestContainerRegistry'
        repository: '$(sendgridContainerRepository)'
        command: 'push'
        tags: |
          latest
          $(tag)
    - task: Docker@2
      inputs:
        containerRegistry: 'ProdContainerRegistry'
        repository: '$(sendgridContainerRepository)'
        command: 'build'
        Dockerfile: '$(sendgridDockerfile)'
        buildContext: '$(Build.SourcesDirectory)'
        tags: |
          latest
          $(tag)
        arguments: '--build-arg FEED_ACCESSTOKEN=$(FEED_ACCESSTOKEN)'
    - task: Docker@2
      inputs:
        containerRegistry: 'ProdContainerRegistry'
        repository: '$(sendgridContainerRepository)'
        command: 'push'
        tags: |
          latest
          $(tag)

  - job: BuildandPublishInternalApiContainer
    displayName: Build and Publish InternalApi Container
    steps:
    - task: UseDotNet@2
      displayName: 'Install .NET 9 SDK'
      inputs:
        packageType: 'sdk'
        version: '9.x'
    - task: Docker@2
      inputs:
        containerRegistry: 'TestContainerRegistry'
        repository: '$(internalApiContainerRepository)'
        command: 'build'
        Dockerfile: '$(internalApiDockerfile)'
        buildContext: '$(Build.SourcesDirectory)'
        tags: |
          latest
          $(tag)
        arguments: '--build-arg FEED_ACCESSTOKEN=$(FEED_ACCESSTOKEN)'
    - task: Docker@2
      inputs:
        containerRegistry: 'TestContainerRegistry'
        repository: '$(internalApiContainerRepository)'
        command: 'push'
        tags: |
          latest
          $(tag)
    - task: Docker@2
      inputs:
        containerRegistry: 'ProdContainerRegistry'
        repository: '$(internalApiContainerRepository)'
        command: 'build'
        Dockerfile: '$(internalApiDockerfile)'
        buildContext: '$(Build.SourcesDirectory)'
        tags: |
          latest
          $(tag)
        arguments: '--build-arg FEED_ACCESSTOKEN=$(FEED_ACCESSTOKEN)'
    - task: Docker@2
      inputs:
        containerRegistry: 'ProdContainerRegistry'
        repository: '$(internalApiContainerRepository)'
        command: 'push'
        tags: |
          latest
          $(tag)

  - job: BuildandPublishExternalApiContainer
    displayName: Build and Publish ExternalApi Container
    steps:
    - task: UseDotNet@2
      displayName: 'Install .NET 9 SDK'
      inputs:
        packageType: 'sdk'
        version: '9.x'
    - task: Docker@2
      inputs:
        containerRegistry: 'TestContainerRegistry'
        repository: '$(ExternalApiContainerRepository)'
        command: 'build'
        Dockerfile: '$(ExternalApiDockerfile)'
        buildContext: '$(Build.SourcesDirectory)'
        tags: |
          latest
          $(tag)
        arguments: '--build-arg FEED_ACCESSTOKEN=$(FEED_ACCESSTOKEN)'
    - task: Docker@2
      inputs:
        containerRegistry: 'TestContainerRegistry'
        repository: '$(ExternalApiContainerRepository)'
        command: 'push'
        tags: |
          latest
          $(tag)
    - task: Docker@2
      inputs:
        containerRegistry: 'ProdContainerRegistry'
        repository: '$(ExternalApiContainerRepository)'
        command: 'build'
        Dockerfile: '$(ExternalApiDockerfile)'
        buildContext: '$(Build.SourcesDirectory)'
        tags: |
          latest
          $(tag)
        arguments: '--build-arg FEED_ACCESSTOKEN=$(FEED_ACCESSTOKEN)'
    - task: Docker@2
      inputs:
        containerRegistry: 'ProdContainerRegistry'
        repository: '$(ExternalApiContainerRepository)'
        command: 'push'
        tags: |
          latest
          $(tag)

  - job: BuildandPublishAzureServiceBusContainer
    displayName: Build and Publish Azure Service Bus Container
    steps:
    - task: UseDotNet@2
      displayName: 'Install .NET 9 SDK'
      inputs:
        packageType: 'sdk'
        version: '9.x'
    - task: Docker@2
      inputs:
        containerRegistry: 'TestContainerRegistry'
        repository: '$(azureServiceBusContainerRepository)'
        command: 'build'
        Dockerfile: '$(azureServiceBusDockerfile)'
        buildContext: '$(Build.SourcesDirectory)'
        tags: |
          latest
          $(tag)
        arguments: '--build-arg FEED_ACCESSTOKEN=$(FEED_ACCESSTOKEN)'
    - task: Docker@2
      inputs:
        containerRegistry: 'TestContainerRegistry'
        repository: '$(azureServiceBusContainerRepository)'
        command: 'push'
        tags: |
          latest
          $(tag)
    - task: Docker@2
      inputs:
        containerRegistry: 'ProdContainerRegistry'
        repository: '$(azureServiceBusContainerRepository)'
        command: 'build'
        Dockerfile: '$(azureServiceBusDockerfile)'
        buildContext: '$(Build.SourcesDirectory)'
        tags: |
          latest
          $(tag)
        arguments: '--build-arg FEED_ACCESSTOKEN=$(FEED_ACCESSTOKEN)'
    - task: Docker@2
      inputs:
        containerRegistry: 'ProdContainerRegistry'
        repository: '$(azureServiceBusContainerRepository)'
        command: 'push'
        tags: |
          latest
          $(tag)

  - job: BuildandPublishShopifyContainer
    displayName: Build and Publish Shopify Container
    steps:
    - task: UseDotNet@2
      displayName: 'Install .NET 9 SDK'
      inputs:
        packageType: 'sdk'
        version: '9.x'
    - task: Docker@2
      inputs:
        containerRegistry: 'TestContainerRegistry'
        repository: '$(shopifyContainerRepository)'
        command: 'build'
        Dockerfile: '$(shopifyDockerfile)'
        buildContext: '$(Build.SourcesDirectory)'
        tags: |
          latest
          $(tag)
        arguments: '--build-arg FEED_ACCESSTOKEN=$(FEED_ACCESSTOKEN)'
    - task: Docker@2
      inputs:
        containerRegistry: 'TestContainerRegistry'
        repository: '$(shopifyContainerRepository)'
        command: 'push'
        tags: |
          latest
          $(tag)
    - task: Docker@2
      inputs:
        containerRegistry: 'ProdContainerRegistry'
        repository: '$(shopifyContainerRepository)'
        command: 'build'
        Dockerfile: '$(shopifyDockerfile)'
        buildContext: '$(Build.SourcesDirectory)'
        tags: |
          latest
          $(tag)
        arguments: '--build-arg FEED_ACCESSTOKEN=$(FEED_ACCESSTOKEN)'
    - task: Docker@2
      inputs:
        containerRegistry: 'ProdContainerRegistry'
        repository: '$(shopifyContainerRepository)'
        command: 'push'
        tags: |
          latest
          $(tag)

  - job: PublishNugetContracts
    displayName: Build and Publish Contracts
    dependsOn: 
    - BuildAndTest
    - BuildandPublishAmazonContainer
    - BuildandPublishInternalApiContainer
    - BuildandPublishExternalApiContainer
    - BuildandPublishAzureServiceBusContainer
    steps:
    - task: UseDotNet@2
      displayName: 'Install .NET 9 SDK'
      inputs:
        packageType: 'sdk'
        version: '9.x'
    - task: DotNetCoreCLI@2
      displayName: Restore
      inputs:
        command: 'restore'
        projects: '$(contractPackageProject)'
        feedsToUse: 'select'
        vstsFeed: '534b90ef-0e1b-475a-8b29-f869c9ffb2fd/202483c3-5d54-4aec-a875-a4bd78523c18'
        verbosityRestore: minimal
    - task: DotNetCoreCLI@2
      displayName: Build
      inputs:
        command: 'build'
        projects: '$(contractPackageProject)'
        arguments: '--no-restore -c Release'
    - task: DotNetCoreCLI@2
      inputs:
        command: 'pack'
        packagesToPack: '$(contractPackageProject)'
        nobuild: true
        versioningScheme: 'byEnvVar'
        versionEnvVar: 'nugetVersion'
        verbosityPack: 'Normal'
    - task: DotNetCoreCLI@2
      inputs:
        command: 'push'
        packagesToPush: '$(Build.ArtifactStagingDirectory)/*.nupkg'
        nuGetFeedType: 'internal'
        publishVstsFeed: '534b90ef-0e1b-475a-8b29-f869c9ffb2fd/202483c3-5d54-4aec-a875-a4bd78523c18'
    
  - job: PublishDeployArtifacts
    displayName: Build and Publish Deployment artifacts
    steps:
    - task: replacetokens@3
      inputs:
        rootDirectory: '$(Build.SourcesDirectory)/deploy'
        targetFiles: '**/*.yml'
        encoding: 'auto'
        writeBOM: false
        actionOnMissing: 'fail'
        keepToken: false
        tokenPrefix: '#{'
        tokenSuffix: '}#'
        useLegacyPattern: false
        enableTelemetry: true
    - task: PublishBuildArtifacts@1
      displayName: Publish deployment files
      inputs:
        PathtoPublish: '$(Build.SourcesDirectory)/deploy'
        ArtifactName: 'Deploy'
        publishLocation: 'Container'
