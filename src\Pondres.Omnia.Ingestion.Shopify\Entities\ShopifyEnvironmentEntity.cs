﻿using Newtonsoft.Json;
using Pondres.Omnia.Ingestion.Common.Config;

namespace Pondres.Omnia.Ingestion.Shopify.Entities;

public class ShopifyEnvironmentEntity(
    string customer, 
    string shopifyAccessTokenSecretName,
    string shopifyUrlSecretName, 
    string flowName)
{
    [JsonProperty("id")]
    public string Customer { get; set; } = customer;

    [JsonProperty("flowName")]
    public string FlowName { get; set; } = flowName;

    [JsonProperty("active")]
    public bool Active { get; set; } = true;

    [JsonProperty("shopifyUrlSecretName")]
    public string ShopifyUrlSecretName { get; set; } = shopifyUrlSecretName;

    [JsonProperty("shopifyApiKeySecretName")]
    public string ShopifyAccessTokenSecretName { get; set; } = shopifyAccessTokenSecretName;

    [JsonProperty("type")]
    public string Type { get; } = CosmosConfiguration.ConfigurationShopifyEnvironmentTypeName;

    [JsonProperty("version")]
    public string Version { get; set; } = "1";
}
