﻿using Microsoft.Azure.Cosmos;
using Pondres.Omnia.Ingestion.Common.Config;
using Solude.ApiBase.Contracts;
using System.Threading.Tasks;

namespace Pondres.Omnia.Ingestion.Common.StartupTasks;

public class CosmosMigrator(
    CosmosClient client) : IDatabaseMigrator
{
    public async Task MigrateAsync()
    {
        var databaseResponse = await CreateDatabaseAsync();

        await CreateContainersAsync(databaseResponse);
    }

    private static async Task CreateContainersAsync(DatabaseResponse database) =>
        await database.Database.CreateContainerIfNotExistsAsync(new ContainerProperties(CosmosConfiguration.ConfigurationContainerName, CosmosConfiguration.ConfigurationContainerPartitionKey));

    private async Task<DatabaseResponse> CreateDatabaseAsync() =>
        await client.CreateDatabaseIfNotExistsAsync(CosmosConfiguration.IngestionDatabaseName, ThroughputProperties.CreateAutoscaleThroughput(1000));
}
