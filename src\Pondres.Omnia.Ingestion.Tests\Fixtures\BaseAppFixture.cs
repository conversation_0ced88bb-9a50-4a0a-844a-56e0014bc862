﻿using FluentAssertions;
using MassTransit;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Pondres.Omnia.Ingestion.Tests.Consumer;
using Pondres.Omnia.Ingestion.Tests.Fixtures.Data;
using Pondres.Omnia.Ingestion.Tests.Helper;
using Solude.ApiBase.Contracts;
using System.Linq.Expressions;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Pondres.Omnia.Ingestion.Tests.Fixtures;

public class BaseAppFixture<TProgram>(ContainersFixture containers) : WebApplicationFactory<TProgram> where TProgram : class
{
    public HttpClient Client { get; private set; } = default!;
    public ContainersFixture Containers { get; } = containers;
    public GlobalConsumeObserver BusConsumeObserver { get; } = new();
    public GlobalPublishObserver BusPublisherObserver { get; } = new();

    public TService ResolveService<TService>() where TService : notnull
    {
        var scope = Services.CreateScope();

        return scope.ServiceProvider.GetRequiredService<TService>();
    }

    public async Task InitializeAsync()
    {
        Client = CreateClient();

        var jsonOptions = new JsonSerializerOptions(JsonSerializerDefaults.Web);
        jsonOptions.Converters.Add(new JsonStringEnumConverter());

        var startupTasks = ResolveService<IEnumerable<IStartupTask>>();
        foreach (var startupTask in startupTasks)
            await startupTask.ExecuteAsync();

        await TestHelper.WaitForAsync(TimeSpan.FromSeconds(45), async () =>
        {
            Console.Out.WriteLine($"Checking if the service is ready");
            var readyResult = await Client.GetAsync("health/ready");
            readyResult.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
        });

        var bus = Services.GetRequiredService<IBus>();
        bus.ConnectConsumeObserver(BusConsumeObserver);
        bus.ConnectPublishObserver(BusPublisherObserver);
    }

    public async Task<TMessageType> PublishAndWaitForConsumeAsync<TMessageType>(
        TMessageType message,
        Expression<Func<TMessageType, bool>> matchFunc) where TMessageType : class
    {
        using var scope = Services.CreateScope();
        var bus = scope.ServiceProvider.GetRequiredService<IBus>();
        await bus.Publish(message);

        return await BusConsumeObserver.Should().HaveSeenMessageAsync(matchFunc);
    }

    public async Task<TMessageType> SendAndWaitForConsumeAsync<TMessageType>(
        TMessageType message,
        string queue,
        Expression<Func<TMessageType, bool>> matchFunc,
        Action<SendContext<TMessageType>>? configureSend = null) where TMessageType : class
    {
        using var scope = Services.CreateScope();
        var bus = scope.ServiceProvider.GetRequiredService<IBus>();
        var endpoint = await bus.GetSendEndpoint(new Uri($"queue:{queue}"));

        if (configureSend != null)
            await endpoint.Send(message, configureSend);
        else
            await endpoint.Send(message);

        return await BusConsumeObserver.Should().HaveSeenMessageAsync(matchFunc);
    }
}
