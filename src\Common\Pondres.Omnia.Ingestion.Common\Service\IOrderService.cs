﻿using Pondres.Omnia.Ingestion.Common.Model;
using Pondres.Omnia.Ingestion.Common.Storage;
using System;
using System.IO;
using System.Threading.Tasks;

namespace Pondres.Omnia.Ingestion.Common.Service;

public interface IOrderService
{
    Task CreateOrderAsync(
        OrderCreationModel orderCreationModel,
        Func<IBlobStorageRepository, Task> saveOrderDataFuncAsync,
        Func<IBlobStorageRepository, Task>? cleanupFuncAsync = null);

    Task CreateSingleRawDataFileOrderAsync(
        Stream sourceStream,
        OrderCreationModel orderCreationModel);
}