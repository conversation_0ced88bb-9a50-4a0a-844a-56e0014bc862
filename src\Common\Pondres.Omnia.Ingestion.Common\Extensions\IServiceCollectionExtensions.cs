﻿using MassTransit;
using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.OpenApi.Models;
using Pondres.Omnia.Ingestion.Common.Config;
using Pondres.Omnia.Ingestion.Common.Provider;
using Pondres.Omnia.Ingestion.Common.Service;
using Pondres.Omnia.Ingestion.Common.StartupTasks;
using Pondres.Omnia.Ingestion.Common.Storage;
using Serilog;
using Solude.ApiBase.Extensions;
using Solude.CustomerProvider;
using Solude.Packages.Common;
using Swashbuckle.AspNetCore.SwaggerGen;
using System;

namespace Pondres.Omnia.Ingestion.Common.Extensions;

public static class IServiceCollectionExtensions
{
    public static IServiceCollection AddCommonDependencies(
        this IServiceCollection services,
        IHostEnvironment environment,
        IngestionAppSettings appSettings,
        Action<IBusRegistrationConfigurator>? configureBus = null)
    {
        services.AddCommonSecretProvider(appSettings);
        services.AddCommonStorage(environment, appSettings);
        services.AddCommonServices();

        services.AddDefaultMassTransit(
            appSettings: appSettings,
            endpointPrefix: "Ingestion",
            configureBus: configureBus ?? (configure => { }));

        return services;
    }

    public static IServiceCollection AddCommonServices(this IServiceCollection services)
    {
        services.AddDatabaseMigrator<CosmosMigrator>();

        services.AddSingleton<IOrderService, OrderService>();
        services.AddSingleton<IEventService, EventService>();

        services.AddCustomerProvider();
        services.AddTransient<CustomerProvider>();

        return services;
    }

    public static IServiceCollection AddCommonStorage(this IServiceCollection services, IHostEnvironment environment, IngestionAppSettings appSettings)
    {
        services.AddSingleton(x => new CosmosClient(appSettings.CosmosDbEndpoint));

        services.AddSingleton<IBlobStorageRepositoryFactory, BlobStorageRepositoryFactory>();

        services.Configure<DataStorageOptions>((options) =>
        {
            options.IsDevelopment = environment.IsDevelopment();
            options.DevelopmentConnectionString = appSettings.AzuriteConnectionString;
        });

        return services;
    }

    public static IServiceCollection AddCommonSecretProvider(this IServiceCollection services, AppSettings appSettings)
    {
        if (appSettings.UseEnvVariablesKeyVault)
        {
            Log.Warning("Using environment variables for secrets");
            services.AddSingleton<ISecretProvider, EnvironmentSecretProvider>();
        }
        else
            services.AddSingleton<ISecretProvider, KeyVaultSecretProvider>();

        return services;
    }

    public static IServiceCollection AddSwaggerIfEnabled(
        this IServiceCollection services,
        bool swaggerEnabled)
    {
        if (swaggerEnabled)
            services.AddSwaggerGen((options =>
            {
                options.SupportNonNullableReferenceTypes();
                options.UseAllOfToExtendReferenceSchemas();
                options.OperationFilter<TokenOperationFilter>();
            }));

        return services;
    }
}

public class TokenOperationFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {

        operation.Parameters ??= [];

        var tokenParameter = new OpenApiParameter
        {
            Name = "x-token",
            In = ParameterLocation.Header,
            Description = "Access token",
            Required = true,
            Schema = new OpenApiSchema { Type = "string" }
        };

        operation.Parameters.Add(tokenParameter);
    }
}
