﻿using Microsoft.Extensions.Caching.Memory;
using Pondres.Omnia.Ingestion.AzureServiceBus.Commands;
using Pondres.Omnia.Ingestion.AzureServiceBus.Config;
using Pondres.Omnia.Ingestion.AzureServiceBus.Queries;
using Pondres.Omnia.Ingestion.AzureServiceBus.Seed;
using Pondres.Omnia.Ingestion.AzureServiceBus.Service;
using Pondres.Omnia.Ingestion.AzureServiceBus.Services;
using Pondres.Omnia.Ingestion.Common.Extensions;
using Solude.ApiBase.Extensions;

namespace Pondres.Omnia.Ingestion.AzureServiceBus;

public class Startup(IHostEnvironment environment, IConfiguration configuration)
{
    private readonly AzureServiceBusAppSettings appSettings = configuration.Get<AzureServiceBusAppSettings>() ??
        throw new InvalidOperationException("AppSettings not bindable");

    public void Configure(IApplicationBuilder app)
    {
        app.UseDefaultServiceDependencies();
    }

    public void ConfigureServices(IServiceCollection services)
    {
        services.AddDefaultServiceDependencies(appSettings, Program.Name);
        services.AddCommonDependencies(environment, appSettings);

        services.AddDataSeed<DataSeed>();

        services.AddHostedService<QueueReaderBackgroundService>();

        services.AddTransient<QueueQueries>();
        services.AddTransient<ReadAzureServiceBusQueueCommandHandler>();
        services.AddSingleton<AzureServiceBusMessageReceiverProvider>();

        services.Configure<DataSeedSettings>(x =>
        {
            x.KeyVaultName = appSettings.TestCustomerKeyVaultName;
            x.StorageAccountName = appSettings.TestCustomerStorageAccountName;
            x.CustomerQueueName = appSettings.TestCustomerQueueName;
        });
    }
}