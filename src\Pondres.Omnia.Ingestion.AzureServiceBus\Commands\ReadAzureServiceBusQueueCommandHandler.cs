﻿using FluentResults;
using FluentValidation;
using Microsoft.Azure.ServiceBus;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Pondres.Omnia.Ingestion.AzureServiceBus.Service;
using Pondres.Omnia.Ingestion.Common.Model;
using Pondres.Omnia.Ingestion.Common.Provider;
using Pondres.Omnia.Ingestion.Common.Service;
using Serilog;
using System.Text;

namespace Pondres.Omnia.Ingestion.AzureServiceBus.Commands;

public record ReadAzureServiceBusQueueCommand(
    string QueueName,
    string FlowName,
    string Customer,
    string CustomerStorageAccountName,
    string CustomerVaultName,
    string ConnectionStringSecretName,
    CustomerReferenceDeterminationType CustomerReferenceDeterminationType,
    string CustomerReferenceDeterminationValue);

public class ReadAzureServiceBusQueueCommandValidator : AbstractValidator<ReadAzureServiceBusQueueCommand>
{
    public ReadAzureServiceBusQueueCommandValidator()
    {
        RuleFor(x => x.QueueName).NotEmpty();
        RuleFor(x => x.ConnectionStringSecretName).NotEmpty();
        RuleFor(x => x.CustomerReferenceDeterminationValue).NotEmpty().When(x => x.CustomerReferenceDeterminationType == CustomerReferenceDeterminationType.JsonPath);
        RuleFor(x => x.CustomerReferenceDeterminationType).NotEmpty().NotEqual(CustomerReferenceDeterminationType.None);
    }
}

public class ReadAzureServiceBusQueueCommandHandler(
    AzureServiceBusMessageReceiverProvider messageReceiverProvider,
    ISecretProvider secretProvider,
    IOrderService orderService,
    IEventService eventService)
{
    private const string flowMessagePropertyKey = "x-flow";
    private readonly int prefetch = 25;

    public async Task<Result> HandleAsync(ReadAzureServiceBusQueueCommand command)
    {
        var validationResult = new ReadAzureServiceBusQueueCommandValidator().Validate(command);

        if (!validationResult.IsValid)
            return Result.Fail(validationResult.Errors.Select(x => x.ErrorMessage));

        var connectionString = await secretProvider.GetSecretAsync(
            vaultName: command.CustomerVaultName,
            secretName: command.ConnectionStringSecretName) ?? throw new ArgumentException($"Secret {command.ConnectionStringSecretName} not found.");

        var messageReceiver = messageReceiverProvider.GetReceiver(connectionString, command.QueueName, prefetch);

        try
        {
            var hasMore = true;

            while (hasMore)
            {
                var messages = await messageReceiver.ReceiveAsync(prefetch);

                Log.Information("Received batch of {MessageCount} messages", messages?.Count);

                if (messages == null || messages.Count == 0)
                    break;

                hasMore = messages.Count == prefetch;

                foreach (var message in messages)
                {
                    try
                    {
                        var customerReference = ExtractCustomerReference(message, command);

                        if (string.IsNullOrEmpty(customerReference))
                        {
                            await PublishIngestionFailedForNullCustomerReferenceAsync(command, message);
                            await messageReceiver.CompleteAsync(message);
                            continue;
                        }

                        var flow = ExtractFlow(message, command);

                        if (string.IsNullOrEmpty(flow))
                        {
                            await PublishIngestionFailedForNullFlowAsync(command, message);
                            await messageReceiver.CompleteAsync(message);
                            continue;
                        }

                        await CreateOrderAsync(command, message, customerReference, flow);
                        await messageReceiver.CompleteAsync(message);
                    }
                    catch (Exception messageException)
                    {
                        Log.Error(messageException, "Failed to process message {MessageId}", message.MessageId);

                        await PublishIngestionFailedForMessageExceptionAsync(command, message, messageException);
                        await messageReceiver.AbandonAsync(message);
                    }
                }

                Log.Information("Processed {MessageCount} messages, has more: {HasMore}", messages.Count, hasMore);
            }

            return Result.Ok();
        }
        catch (Exception exception)
        {
            await PublishIngestionFailedForExceptionAsync(eventService, command, exception);

            return new ExceptionalError(exception);
        }
    }

    private static string? ExtractFlow(Message message, ReadAzureServiceBusQueueCommand command)
    {
        if (!string.IsNullOrEmpty(command.FlowName))
            return command.FlowName;

        return message.UserProperties.TryGetValue(flowMessagePropertyKey, out var value) ? value.ToString() : null;
    }

    private static async Task PublishIngestionFailedForExceptionAsync(IEventService eventService, ReadAzureServiceBusQueueCommand command, Exception exception)
    {
        Log.Error(exception, "Exception during handling of ReadAzureServiceBusQueueCommand");

        await eventService.PublishIngestionFailureAsync(
            exception: exception,
            requestId: Guid.NewGuid().ToString(),
            flowName: command.FlowName,
            customer: command.Customer);
    }

    private async Task CreateOrderAsync(ReadAzureServiceBusQueueCommand command, Message message, string customerReference, string flow)
    {
        using var messageStream = new MemoryStream(message.Body);

        var createOrderModel = new OrderCreationModel(
            CustomerReference: customerReference,
            Categories: new OrderCreationModelCategories(),
            Customer: command.Customer,
            FileName: $"{message.MessageId}.json",
            FlowName: flow,
            FlowId: null,
            OrderId: Guid.NewGuid(),
            RequestId: message.MessageId,
            SourceName: "AzureServiceBus",
            CustomerStorageAccountName: command.CustomerStorageAccountName);

        await orderService.CreateSingleRawDataFileOrderAsync(messageStream, createOrderModel);
    }

    private async Task PublishIngestionFailedForNullCustomerReferenceAsync(ReadAzureServiceBusQueueCommand command, Message message)
    {
        Log.Error("Customer reference could not be determined for message Id: {MessageId}", message.MessageId);

        await eventService.PublishIngestionFailureAsync(
            message: "Customer reference could not be determined",
            data: $"Determination Value: {command.CustomerReferenceDeterminationValue} Message body: {Encoding.UTF8.GetString(message.Body)}",
            requestId: Guid.NewGuid().ToString(),
            flowName: command.FlowName,
            customer: command.Customer);
    }

    private async Task PublishIngestionFailedForNullFlowAsync(ReadAzureServiceBusQueueCommand command, Message message)
    {
        Log.Error("Flow was not present as an x-flow message property or in the configuration: {MessageId}", message.MessageId);

        await eventService.PublishIngestionFailureAsync(
            message: "Flow could not be determined",
            data: $"Configuration flow: '{command.FlowName}' Message properties: '{JsonConvert.SerializeObject(message.UserProperties)}'",
            requestId: Guid.NewGuid().ToString(),
            flowName: string.Empty,
            customer: command.Customer);
    }

    private async Task PublishIngestionFailedForMessageExceptionAsync(ReadAzureServiceBusQueueCommand command, Message message, Exception exception)
    {
        Log.Error(exception, "Exception during processing of message {MessageId}", message.MessageId);

        await eventService.PublishIngestionFailureAsync(
            exception: exception,
            requestId: message.MessageId,
            flowName: command.FlowName,
            customer: command.Customer);
    }

    private static string? ExtractCustomerReference(Message message, ReadAzureServiceBusQueueCommand command) =>
        command.CustomerReferenceDeterminationType switch
        {
            CustomerReferenceDeterminationType.JsonPath => ExtractCustomerReferenceFromContent(message, command),
            CustomerReferenceDeterminationType.UserMessageProperty => ExtractCustomerReferenceFromUserMessageProperties(message, command),
            _ => throw new NotImplementedException($"No customer reference determination type implemented for type {command.CustomerReferenceDeterminationType}")
        };

    private static string? ExtractCustomerReferenceFromContent(Message message, ReadAzureServiceBusQueueCommand command)
    {
        var jsonString = Encoding.UTF8.GetString(message.Body);

        var json = JObject.Parse(jsonString);

        return json?.SelectToken(command.CustomerReferenceDeterminationValue)?.Value<string>();
    }

    private static string? ExtractCustomerReferenceFromUserMessageProperties(Message message, ReadAzureServiceBusQueueCommand command)
    {
        message.UserProperties.TryGetValue(command.CustomerReferenceDeterminationValue, out var value);

        return value?.ToString();
    }
}
