﻿using Moq;
using Moq.Protected;
using ShopifySharp;
using System.Net;
using System.Net.Http.Json;
using System.Text;

namespace Pondres.Omnia.Ingestion.Tests.Fixtures.Data;

public class ShopifyApiFixture(Mock<IHttpClientFactory> factoryMock)
{
    public Mock<HttpMessageHandler> SetupHttpClientFactoryHandler()
    {
        var senderMock = new Mock<HttpMessageHandler>(MockBehavior.Strict);

        var client = new HttpClient(senderMock.Object);

        factoryMock.Setup(x => x.CreateClient("Shopify")).Returns(client);

        return senderMock;
    }
}

public static class HttpMessageHandlerExtensions
{
    public static Mock<HttpMessageHandler> SetupShopifyOrders(this Mock<HttpMessageHandler> httpMessageHandlerMock, string baseShopifyUrl)
    {
        var responseJson = @"
        {
            ""fulfillment_orders"": [ { } ]
        }";

        var response = new HttpResponseMessage
        {
            StatusCode = HttpStatusCode.OK,
            Content = new StringContent(responseJson, Encoding.UTF8, "application/json")
        };

        httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                 "SendAsync",
                 ItExpr.Is<HttpRequestMessage>(x => x.RequestUri!.ToString() == $"{baseShopifyUrl}/admin/api/2024-10/assigned_fulfillment_orders.json?assignment_status=fulfillment_requested"),
                 ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(response)
            .Verifiable();

        return httpMessageHandlerMock;
    }
}
