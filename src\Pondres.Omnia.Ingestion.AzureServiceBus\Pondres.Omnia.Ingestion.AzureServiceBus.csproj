﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<UserSecretsId>883c3c07-18db-4aac-91a6-f0938d86ae7d</UserSecretsId>
		<DockerfileContext>..\src</DockerfileContext>
		<DockerfileContext>..\..</DockerfileContext>
		<DockerComposeProjectPath>..\..\docker-compose.dcproj</DockerComposeProjectPath>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="FluentResults" Version="4.0.0" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.22.0" />
		<PackageReference Include="Microsoft.Azure.ServiceBus" Version="5.2.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Common\Pondres.Omnia.Ingestion.Common\Pondres.Omnia.Ingestion.Common.csproj" />
	</ItemGroup>

</Project>
