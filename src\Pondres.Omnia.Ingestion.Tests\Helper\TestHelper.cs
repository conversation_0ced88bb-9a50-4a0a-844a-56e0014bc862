﻿namespace Pondres.Omnia.Ingestion.Tests.Helper;

public static class TestHelper
{
    public static string GenerateCustomerName() => Guid.NewGuid().ToString("N");

    public static async Task WaitForAsync(
        TimeSpan wait,
        Func<Task> forFunc,
        CancellationToken cancellationToken = default)
    {
        var start = DateTime.Now;

        while (!cancellationToken.IsCancellationRequested)
            try
            {
                await forFunc();
                return;
            }
            catch (Exception exception)
            {
                Console.Out.WriteLine($"Exception during waiting: {exception.Message}");

                if (start + wait < DateTime.Now)
                    throw;
                else
                    await Task.Delay(200, cancellationToken);
            }
    }

    public static async Task<TResult> WaitForAsync<TResult>(
        TimeSpan wait,
        Func<Task<TResult>> forFunc,
        CancellationToken cancellationToken = default)
    {
        var start = DateTime.Now;

        while (!cancellationToken.IsCancellationRequested)
            try
            {
                return await forFunc();
            }
            catch (Exception exception)
            {
                Console.Out.WriteLine($"Exception during waiting: {exception.Message}");

                if (start + wait < DateTime.Now)
                    throw;
                else
                    await Task.Delay(200, cancellationToken);
            }

        // Should never reach this
        return default!;
    }
}
