﻿using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Options;
using Pondres.Omnia.Ingestion.AzureServiceBus.Entities;
using Pondres.Omnia.Ingestion.Common.Config;
using Pondres.Omnia.Ingestion.Common.Model;
using Solude.ApiBase.Contracts;

namespace Pondres.Omnia.Ingestion.AzureServiceBus.Seed;

public class DataSeedSettings
{
    public string StorageAccountName { get; set; } = string.Empty;
    public string KeyVaultName { get; set; } = string.Empty;
    public string CustomerQueueName { get; set; } = string.Empty;
}

public class DataSeed(CosmosClient cosmosClient, IOptions<DataSeedSettings> options) : IDataSeed
{
    private readonly DataSeedSettings settings = options.Value;

    public async Task SeedAsync()
    {
        var registrationsEntity = new AzureServiceBusQueueRegistrationsEntity(
            customer: "K00021",
            vaultName: settings.KeyVaultName,
            storageAccountName: settings.StorageAccountName);

        registrationsEntity.Queues.Add(new AzureServiceBusCustomerQueueRegistrationEntity(
            queueName: settings.CustomerQueueName,
            flowName: "AzureServiceBus",
            connectionStringSecretName: "AzureServiceBusConnectionString",
            customerReferenceDeterminationType: CustomerReferenceDeterminationType.JsonPath,
            customerReferenceDeterminationValue: "$.customerReference"));

        registrationsEntity.Queues.Add(new AzureServiceBusCustomerQueueRegistrationEntity(
            queueName: "ingestion_k00021",
            flowName: "",
            connectionStringSecretName: "AzureServiceBusConnectionString",
            customerReferenceDeterminationType: CustomerReferenceDeterminationType.UserMessageProperty,
            customerReferenceDeterminationValue: "x-customer-reference"));

        var container = cosmosClient.GetContainer(CosmosConfiguration.IngestionDatabaseName, CosmosConfiguration.ConfigurationContainerName);

        try
        {
            await container.CreateItemAsync(registrationsEntity);
        }
        catch (CosmosException exception) when (exception.StatusCode == System.Net.HttpStatusCode.Conflict)
        {
            await container.ReplaceItemAsync(
                item: registrationsEntity,
                id: registrationsEntity.Customer,
                partitionKey: new PartitionKey(registrationsEntity.Type));
        }
    }
}
