﻿using MassTransit;
using Pondres.Omnia.Ingestion.Common.Service;
using Pondres.Omnia.Ingestion.SendGrid.Commands;
using Pondres.Omnia.Ingestion.SendGrid.Models;
using Serilog;

namespace Pondres.Omnia.Ingestion.SendGrid.Consumers;

public class SendGridBulkIngestionConsumerDefinition : ConsumerDefinition<SendGridBulkIngestionConsumer>
{
    public SendGridBulkIngestionConsumerDefinition()
    {
        EndpointName = "ingestionservice_sendgrid_bulk";
    }

    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<SendGridBulkIngestionConsumer> consumerConfigurator,
        IRegistrationContext context)
    {
        // Allows for ingesting raw messages from the bus without the MassTransit envelope
        endpointConfigurator.ConfigureConsumeTopology = false;
        endpointConfigurator.ClearSerialization();
        endpointConfigurator.UseRawJsonSerializer();
    }
}

public class SendGridBulkIngestionConsumer(
    IngestBulkOrderForTriggerFileCommandHandler commandHandler) : IConsumer<SendGridBlobCreatedEvent>
{
    public async Task Consume(ConsumeContext<SendGridBlobCreatedEvent> context)
    {
        try
        {
            var command = new IngestBulkOrderForTriggerFileCommand(
                FlowName: context.Headers.Get<string>("x-flow") ?? string.Empty,
                Customer: context.Headers.Get<string>("x-customer")?.ToUpper() ?? string.Empty,
                Subject: context.Message.Subject,
                RequestId: context.Message.Id);

            Log.Debug("Received command {@Command}", command);


            await commandHandler.HandleAsync(command);
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Failed to process SendGrid event");

            //Don't rethrow, because masstransit will send out a Fault message that will end up in the skipped queue because it doesn't have the masstransit envelope
        }
    }
}
