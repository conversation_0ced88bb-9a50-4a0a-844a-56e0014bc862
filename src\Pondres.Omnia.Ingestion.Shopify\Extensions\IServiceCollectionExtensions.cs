﻿using StackExchange.Redis;

namespace Pondres.Omnia.Ingestion.Shopify.Extensions;
public static class IServiceCollectionExtensions
{
    public static IServiceCollection AddRedis(this IServiceCollection services, string redisConnectionString)
    {
        var redisMultiplexer = ConnectionMultiplexer.Connect(redisConnectionString);

        services.AddSingleton<IConnectionMultiplexer>(redisMultiplexer);

        return services;
    }
}
