﻿using Azure.Storage.Blobs;
using Pondres.Omnia.OrderHub.Contracts.Order;

namespace Pondres.Omnia.Ingestion.Tests.Fixtures.Data;

public class BlobFixture(
    string azuriteConnectionString)
{
    private const string orderdDataContainer = "orderdata";
    private readonly BlobServiceClient serviceClient = new(azuriteConnectionString);

    public async Task CreateOrderDataContainerAsync()
    {
        var containerClient = serviceClient.GetBlobContainerClient(orderdDataContainer);

        await containerClient.CreateIfNotExistsAsync();
    }
    
    public async Task<BlobContainerClient> GetOrCreateContainerAsync(string containerName)
    {
        var containerClient = serviceClient.GetBlobContainerClient(containerName);

        await containerClient.CreateIfNotExistsAsync();

        return containerClient;
    }

    public async Task<TContent> DownloadOrderDataAsync<TContent>(CreateOrderMessage message, string? path = null)
    {
        path ??= $"{message.Flow}/{message.OrderId}/RawData/{message.RequestId}.json";

        var containerClient = serviceClient.GetBlobContainerClient(orderdDataContainer);

        var blobClient = containerClient.GetBlobClient(path);

        var blobContent = await blobClient.DownloadContentAsync();

        return blobContent.Value.Content.ToObjectFromJson<TContent>()!;
    }
}
